# Ringkasan Alur Aplikasi AquaTemp IoT

## 🎯 **Alur Utama Aplikasi**

### 1. **<PERSON><PERSON><PERSON> Aplik<PERSON>**
```
<PERSON><PERSON> App → Inisialisasi Supabase → Cek <PERSON> → Halaman Utama → Siap Digunakan
```

### 2. **Proses Login**
```
Input Email/Password → Validasi → Buat Data User → Masuk ke Dashboard
```

### 3. **Monitoring Real-time**
```
Sensor ESP32 → Baca Suhu → Kirim ke Flutter → Simpan Database → Tampil di UI
```

### 4. **Kontrol Otomatis**
```
<PERSON><PERSON><PERSON> Suhu → Tentukan Aksi → Kirim Perintah → Kontrol Relay → Ubah Suhu
```

---

## 🔄 **Siklus Kerja Sistem**

### **Setiap 10 Detik: Pengumpulan Data**
1. ESP32 baca suhu dari sensor DS18B20
2. Kirim data via WiFi ke aplikasi Flutter
3. Simpan data ke database Supabase
4. Update tampilan UI secara real-time

### **Setiap 5 Detik: <PERSON><PERSON><PERSON>**
1. <PERSON><PERSON><PERSON> terbaru vs pengaturan user
2. Tentukan aksi: nyalakan pemanas/pompa/matikan
3. Kirim perintah ke ESP32
4. Catat aktivitas ke riwayat

### **Real-time: Update Antarmuka**
1. Database kirim perubahan via stream
2. Layanan aplikasi terima update
3. UI rebuild otomatis
4. User lihat data terbaru

---

## 📱 **Navigasi Halaman**

### **🏠 Halaman Beranda**
- Tampil suhu air saat ini
- Status pemanas dan pompa
- Grafik penggunaan
- Tombol kontrol manual

### **⚙️ Halaman Pengaturan Suhu**
- Atur batas suhu minimum
- Atur batas suhu maksimum
- Validasi input
- Simpan pengaturan

### **📊 Halaman Riwayat**
- Lihat riwayat penggunaan
- Filter berdasarkan tanggal
- Statistik penggunaan
- Export data

### **👤 Halaman Profil**
- Info profil pengguna
- Edit nama dan foto
- Akses kontrol ESP32
- Logout

---

## 🌡️ **Logika Kontrol Suhu**

### **Tabel Keputusan:**
| Kondisi Suhu | Pemanas | Pompa | Hasil |
|--------------|---------|-------|-------|
| < Batas Minimum | HIDUP | MATI | Air dipanaskan |
| > Batas Maksimum | MATI | HIDUP | Air didinginkan |
| Dalam Rentang Normal | MATI | MATI | Suhu dipertahankan |

### **Fitur Keamanan:**
- ⏱️ Jeda minimum 30 detik antar aksi
- 🔄 Auto retry 3x untuk perintah gagal
- ⚠️ Alert jika ESP32 offline
- 📝 Log semua aktivitas

---

## 🔌 **Komunikasi Hardware**

### **Flutter → ESP32:**
```
User Aksi → Layanan → HTTP POST → ESP32 → GPIO → Relay → Perangkat
```

### **ESP32 → Flutter:**
```
Sensor DS18B20 → ESP32 → HTTP Response → Flutter → Database → UI
```

### **API Endpoints:**
- `GET /api/sensor` - Baca data sensor
- `POST /api/command` - Kirim perintah kontrol
- `GET /api/ping` - Cek status koneksi
- `GET /api/status` - Info perangkat

---

## 🗄️ **Struktur Database**

### **Tabel Utama:**
- **users** - Data profil pengguna
- **sensor_data** - Pembacaan suhu
- **temperature_settings** - Pengaturan batas suhu
- **heater_status** - Status pemanas saat ini
- **heater_history** - Riwayat penggunaan
- **esp32_devices** - Info perangkat ESP32
- **relay_controls** - Konfigurasi relay

### **Alur Data:**
```
Input User → Validasi → Database → Stream → Update UI
```

---

## ⚙️ **Arsitektur Layanan**

### **Layer Antarmuka Pengguna:**
- Halaman Login, Beranda, Pengaturan, Riwayat, Profil

### **Layer Layanan:**
- Layanan Autentikasi, Database, Sensor, ESP32, Kontrol Otomatis

### **Layer Model:**
- Model data untuk User, Sensor, Pengaturan, Status, dll

### **Layer Eksternal:**
- Supabase Auth, Database, Hardware ESP32

---

## 🚀 **Proses Startup Lengkap**

```
1. Buka Aplikasi
   ↓
2. Inisialisasi Supabase
   ↓
3. Cek Status Login
   ↓
4. Login (jika perlu)
   ↓
5. Inisialisasi Semua Layanan
   ↓
6. Hubungkan ke ESP32
   ↓
7. Mulai Monitoring Real-time
   ↓
8. Aktifkan Kontrol Otomatis
   ↓
9. Tampilkan Dashboard
   ↓
10. Siap Digunakan!
```

---

## 🔧 **Penanganan Error**

### **Jenis Error:**
- **Jaringan** - Koneksi WiFi bermasalah
- **Hardware** - ESP32 tidak merespon
- **Database** - Supabase error
- **Validasi** - Input user salah

### **Solusi Otomatis:**
- Retry otomatis untuk perintah gagal
- Fallback ke mode manual jika auto gagal
- Notifikasi user jika ada masalah
- Log error untuk debugging

---

## 📊 **Monitoring Performa**

### **Metrik Utama:**
- ⚡ Waktu respon < 5 detik
- 🔗 Uptime > 99%
- 🎯 Akurasi suhu ±0.5°C
- ✅ Keberhasilan perintah > 99%

### **Optimisasi:**
- Caching data yang sering diakses
- Batching request untuk efisiensi
- Lazy loading untuk data besar
- Memory management yang baik

---

## 🎯 **Ringkasan Sistem**

**AquaTemp IoT** adalah sistem monitoring dan kontrol suhu air otomatis yang terdiri dari:

### **🔧 Hardware:**
- ESP32 + Sensor DS18B20 + 2 Relay
- Pemanas air dan pompa air
- Koneksi WiFi

### **📱 Software:**
- Aplikasi Flutter dengan UI yang intuitif
- Real-time monitoring dan kontrol
- Database cloud dengan Supabase

### **🌐 Komunikasi:**
- HTTP API untuk komunikasi
- Real-time streams untuk update
- WiFi untuk konektivitas

### **🎛️ Fitur Utama:**
- ✅ Monitoring suhu real-time
- ✅ Kontrol otomatis pemanas/pompa
- ✅ Pengaturan batas suhu custom
- ✅ Riwayat penggunaan lengkap
- ✅ Kontrol manual override
- ✅ Multi-user support
- ✅ Error handling yang robust

**Sistem ini memberikan solusi lengkap untuk monitoring dan kontrol suhu air secara otomatis dengan antarmuka yang mudah digunakan!** 🌊📱⚡
