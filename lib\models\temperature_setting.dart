class TemperatureSetting {
  final double minTemperature;
  final double maxTemperature;
  final String userId;
  final String pengaturanId;
  final DateTime? updatedAt;

  TemperatureSetting({
    required this.minTemperature,
    required this.maxTemperature,
    required this.userId,
    required this.pengaturanId,
    this.updatedAt,
  });

  factory TemperatureSetting.fromJson(Map<String, dynamic> json) {
    return TemperatureSetting(
      minTemperature: (json['min_temperature'] as num).toDouble(),
      maxTemperature: (json['max_temperature'] as num).toDouble(),
      userId: json['user_id'] as String,
      pengaturanId: json['pengaturan_id'] as String,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min_temperature': minTemperature,
      'max_temperature': maxTemperature,
      'user_id': userId,
      'pengaturan_id': pengaturanId,
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Method untuk validasi range suhu
  bool isValidRange() {
    return minTemperature < maxTemperature && 
           minTemperature >= 0 && 
           maxTemperature <= 100;
  }

  // Method untuk cek apakah suhu dalam range
  bool isTemperatureInRange(double temperature) {
    return temperature >= minTemperature && temperature <= maxTemperature;
  }

  // Method untuk mendapatkan status suhu
  TemperatureStatus getTemperatureStatus(double currentTemperature) {
    if (currentTemperature < minTemperature) {
      return TemperatureStatus.tooLow;
    } else if (currentTemperature > maxTemperature) {
      return TemperatureStatus.tooHigh;
    } else {
      return TemperatureStatus.normal;
    }
  }

  TemperatureSetting copyWith({
    double? minTemperature,
    double? maxTemperature,
    String? userId,
    String? pengaturanId,
    DateTime? updatedAt,
  }) {
    return TemperatureSetting(
      minTemperature: minTemperature ?? this.minTemperature,
      maxTemperature: maxTemperature ?? this.maxTemperature,
      userId: userId ?? this.userId,
      pengaturanId: pengaturanId ?? this.pengaturanId,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'TemperatureSetting(pengaturanId: $pengaturanId, range: $minTemperature°C - $maxTemperature°C, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TemperatureSetting && other.pengaturanId == pengaturanId;
  }

  @override
  int get hashCode => pengaturanId.hashCode;
}

enum TemperatureStatus {
  tooLow,
  normal,
  tooHigh,
}

extension TemperatureStatusExtension on TemperatureStatus {
  String get displayName {
    switch (this) {
      case TemperatureStatus.tooLow:
        return 'Terlalu Rendah';
      case TemperatureStatus.normal:
        return 'Normal';
      case TemperatureStatus.tooHigh:
        return 'Terlalu Tinggi';
    }
  }

  String get description {
    switch (this) {
      case TemperatureStatus.tooLow:
        return 'Suhu di bawah batas minimum';
      case TemperatureStatus.normal:
        return 'Suhu dalam rentang normal';
      case TemperatureStatus.tooHigh:
        return 'Suhu di atas batas maksimum';
    }
  }
}
