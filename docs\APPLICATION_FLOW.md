# Alur Aplikasi AquaTemp IoT

## 🚀 1. APPLICATION STARTUP FLOW

```mermaid
graph TD
    A[App Start] --> B[main.dart]
    B --> C[Initialize Supabase]
    C --> D[Run MyApp]
    D --> E[AuthWrapper]
    E --> F{Check Auth Status}
    F -->|Logged In| G[MainPage]
    F -->|Not Logged In| H[LoginPage]

    G --> I[Initialize Services]
    I --> J[DatabaseService]
    I --> K[SensorService]
    I --> L[HeaterService]
    I --> M[ESP32Service]
    I --> N[AutoControlService]
```

### Startup Sequence:
1. **main.dart** - Entry point aplikasi
2. **Supabase.initialize()** - Setup koneksi database
3. **AuthWrapper** - Cek status authentication
4. **Service Initialization** - Initialize semua services
5. **UI Rendering** - Render halaman sesuai auth status

---

## 🔐 2. AUTHENTICATION FLOW

```mermaid
graph TD
    A[LoginPage] --> B[User Input Email/Password]
    B --> C[AuthService.signInWithEmailPassword]
    C --> D{Authentication}
    D -->|Success| E[Create/Update User Data]
    E --> F[Navigate to MainPage]
    D -->|Failed| G[Show Error Message]
    G --> A

    F --> H[Initialize User Services]
    H --> I[Load User Settings]
    H --> J[Start Real-time Monitoring]
```

### Authentication Steps:
1. **Login Input** - User masukkan email/password
2. **Supabase Auth** - Validasi credentials
3. **User Data Sync** - Sync data user ke database
4. **Service Setup** - Initialize services untuk user
5. **Real-time Start** - Mulai monitoring real-time

---

## 🏠 3. MAIN APPLICATION FLOW

```mermaid
graph TD
    A[MainPage] --> B[Bottom Navigation]
    B --> C[HomePage]
    B --> D[UpdateSuhuPage]
    B --> E[RiwayatPage]
    B --> F[ProfilPage]

    C --> G[Real-time Monitoring]
    C --> H[Temperature Display]
    C --> I[Heater Control]
    C --> J[Statistics Chart]

    D --> K[Temperature Settings]
    D --> L[Min/Max Configuration]

    E --> M[History Data]
    E --> N[Usage Statistics]

    F --> O[User Profile]
    F --> P[Logout Function]
    F --> Q[ESP32 Control Access]
```

### Main Navigation:
- **Home** - Dashboard utama dengan monitoring
- **Update Suhu** - Pengaturan batas suhu
- **Riwayat** - History penggunaan heater
- **Profil** - User profile dan settings

---

## 📊 4. REAL-TIME MONITORING FLOW

```mermaid
graph TD
    A[SensorService] --> B{Data Source}
    B -->|ESP32 Connected| C[ESP32Service.readSensorData]
    B -->|Simulation Mode| D[Generate Random Data]

    C --> E[HTTP Request to ESP32]
    E --> F[DS18B20 Sensor Reading]
    F --> G[Return Temperature Data]

    D --> H[Simulated Sensor Data]

    G --> I[Store to Database]
    H --> I
    I --> J[Update UI Stream]
    J --> K[HomePage Display]
    J --> L[Auto Control Check]
```

### Monitoring Process:
1. **Data Collection** - Dari ESP32 atau simulasi
2. **Database Storage** - Simpan ke sensor_data table
3. **Stream Update** - Update UI real-time
4. **Auto Control** - Trigger kontrol otomatis

---

## 🌡️ 5. TEMPERATURE CONTROL FLOW

```mermaid
graph TD
    A[New Temperature Data] --> B[AutoControlService]
    B --> C{Auto Mode Enabled?}
    C -->|No| D[Manual Control Only]
    C -->|Yes| E[Check Temperature Status]

    E --> F{Temperature vs Settings}
    F -->|< Min Temp| G[Turn ON Heater]
    F -->|> Max Temp| H[Turn ON Pump]
    F -->|Normal Range| I[Turn OFF Both]

    G --> J[ESP32Service.controlRelay]
    H --> J
    I --> J

    J --> K[Send HTTP Command]
    K --> L[ESP32 Relay Control]
    L --> M[Update Database Status]
    M --> N[Log History]
    N --> O[Update UI]
```

### Control Logic:
1. **Temperature Analysis** - Bandingkan dengan setting
2. **Decision Making** - Tentukan aksi yang diperlukan
3. **Device Control** - Kirim command ke ESP32
4. **Status Update** - Update database dan UI
5. **History Logging** - Catat aktivitas

---

## 🔌 6. ESP32 COMMUNICATION FLOW

```mermaid
graph TD
    A[Flutter App] --> B[ESP32Service]
    B --> C{Command Type}
    C -->|Read Sensor| D[GET /api/sensor]
    C -->|Control Relay| E[POST /api/command]
    C -->|Check Status| F[GET /api/ping]

    D --> G[ESP32 Web Server]
    E --> G
    F --> G

    G --> H[DS18B20 Reading]
    G --> I[Relay Control]
    G --> J[Status Response]

    H --> K[Return JSON Data]
    I --> K
    J --> K

    K --> L[Flutter Response Handler]
    L --> M[Update Local State]
    M --> N[Update Database]
    N --> O[Update UI]
```

### Communication Steps:
1. **Command Generation** - Flutter buat HTTP request
2. **Network Transfer** - Kirim ke ESP32 via WiFi
3. **ESP32 Processing** - Process command di ESP32
4. **Hardware Action** - Control sensor/relay
5. **Response Return** - Kirim response balik
6. **State Update** - Update state di Flutter

---

## 📱 7. USER INTERFACE FLOW

```mermaid
graph TD
    A[User Interaction] --> B{Action Type}
    B -->|View Data| C[Stream Subscription]
    B -->|Change Settings| D[Update Database]
    B -->|Manual Control| E[Send Command]

    C --> F[Real-time Updates]
    F --> G[UI Rebuild]

    D --> H[Validate Input]
    H --> I[Save to Database]
    I --> J[Update Services]
    J --> G

    E --> K[Check Permissions]
    K --> L[Execute Command]
    L --> M[Show Feedback]
    M --> G
```

### UI Interaction Types:
- **Passive Viewing** - Real-time data display
- **Settings Change** - Update konfigurasi
- **Active Control** - Manual device control
- **Navigation** - Pindah antar halaman

---

## 🗄️ 8. DATABASE OPERATIONS FLOW

```mermaid
graph TD
    A[Database Operation] --> B{Operation Type}
    B -->|Read| C[SELECT Query]
    B -->|Write| D[INSERT Query]
    B -->|Update| E[UPDATE Query]
    B -->|Stream| F[Real-time Subscription]

    C --> G[Supabase Client]
    D --> G
    E --> G
    F --> G

    G --> H[PostgreSQL Database]
    H --> I[Return Results]
    I --> J[Model Conversion]
    J --> K[Service Layer]
    K --> L[UI Update]
```

### Database Tables:
- **users** - User profile data
- **sensor_data** - Temperature readings
- **temperature_settings** - User preferences
- **heater_status** - Current heater state
- **heater_history** - Usage history
- **esp32_devices** - Device information
- **relay_controls** - Relay configurations

---

## ⚙️ 9. SERVICE LAYER ARCHITECTURE

```mermaid
graph TD
    A[UI Layer] --> B[Service Layer]
    B --> C[AuthService]
    B --> D[DatabaseService]
    B --> E[SensorService]
    B --> F[HeaterService]
    B --> G[ESP32Service]
    B --> H[AutoControlService]

    C --> I[Supabase Auth]
    D --> J[Supabase Database]
    E --> K[Real-time Streams]
    F --> L[Device Control]
    G --> M[HTTP Communication]
    H --> N[Automation Logic]

    I --> O[External Services]
    J --> O
    K --> P[Data Sources]
    L --> P
    M --> Q[ESP32 Hardware]
    N --> R[Control Logic]
```

### Service Responsibilities:
- **AuthService** - Authentication management
- **DatabaseService** - Database operations
- **SensorService** - Sensor data handling
- **HeaterService** - Heater control logic
- **ESP32Service** - Hardware communication
- **AutoControlService** - Automation logic

---

## 🔄 10. COMPLETE SYSTEM FLOW

```mermaid
graph TD
    A[ESP32 Hardware] --> B[DS18B20 Sensor]
    B --> C[Temperature Reading]
    C --> D[ESP32 Web Server]
    D --> E[HTTP API]

    E --> F[Flutter App]
    F --> G[ESP32Service]
    G --> H[SensorService]
    H --> I[AutoControlService]

    I --> J{Auto Control Logic}
    J -->|Heat Needed| K[Heater ON Command]
    J -->|Cool Needed| L[Pump ON Command]
    J -->|Normal Temp| M[All OFF Command]

    K --> N[ESP32Service.controlRelay]
    L --> N
    M --> N

    N --> O[HTTP Command to ESP32]
    O --> P[Relay Control]
    P --> Q[Heater/Pump Action]

    Q --> R[Temperature Change]
    R --> B

    F --> S[DatabaseService]
    S --> T[Supabase Database]
    T --> U[Data Storage]

    F --> V[UI Updates]
    V --> W[User Interface]
    W --> X[User Interaction]
    X --> F
```

---

## 📋 11. ERROR HANDLING FLOW

```mermaid
graph TD
    A[Operation Start] --> B{Try Operation}
    B -->|Success| C[Return Result]
    B -->|Error| D[Catch Exception]

    D --> E{Error Type}
    E -->|Network Error| F[Retry Logic]
    E -->|Auth Error| G[Redirect to Login]
    E -->|Validation Error| H[Show User Message]
    E -->|System Error| I[Log Error]

    F --> J{Retry Count}
    J -->|< Max Retries| B
    J -->|Max Reached| K[Fail Gracefully]

    G --> L[Clear Session]
    H --> M[User Feedback]
    I --> N[Debug Logging]
    K --> M

    L --> O[LoginPage]
    M --> P[Continue Operation]
    N --> P
```

### Error Types:
- **Network Errors** - Connection issues
- **Authentication Errors** - Login problems
- **Validation Errors** - Input validation
- **Hardware Errors** - ESP32 communication
- **Database Errors** - Supabase issues

---

## 🎯 12. STARTUP TO OPERATION COMPLETE FLOW

```
1. App Launch
   ↓
2. Supabase Initialize
   ↓
3. Auth Check
   ↓
4. Login (if needed)
   ↓
5. Service Initialization
   ↓
6. ESP32 Connection
   ↓
7. Real-time Monitoring Start
   ↓
8. Auto Control Enable
   ↓
9. User Interface Ready
   ↓
10. Continuous Operation
```

### Operational States:
- **Startup** - Initializing components
- **Authentication** - User login process
- **Connection** - Connecting to ESP32
- **Monitoring** - Real-time data collection
- **Control** - Automatic temperature control
- **Ready** - Full operational state

---

## 📊 13. DATA FLOW SUMMARY

```
Hardware → ESP32 → HTTP API → Flutter App → Database → UI
    ↑                                                    ↓
    └─────────── Control Commands ←─────────────────────┘
```

### Key Data Flows:
1. **Sensor Data**: ESP32 → Flutter → Database → UI
2. **Control Commands**: UI → Flutter → ESP32 → Hardware
3. **User Settings**: UI → Database → Services → ESP32
4. **History Data**: Database → Services → UI
5. **Real-time Updates**: Database → Streams → UI

## 🔄 14. REAL-TIME DATA FLOW

### Sensor Data Flow:
```
ESP32 DS18B20 → HTTP API → Flutter App → Database → UI Stream → User Display
     ↑                                                              ↓
     └─────────────── Control Commands ←─────────────────────────────┘
```

### Control Command Flow:
```
User Action → UI → Service → HTTP Command → ESP32 → Relay → Device → Temperature Change
```

### Database Sync Flow:
```
Local State ↔ Supabase Database ↔ Real-time Streams ↔ UI Updates
```

---

## 📊 15. STATE MANAGEMENT FLOW

```mermaid
graph TD
    A[User Action] --> B[Service Method Call]
    B --> C[Update Local State]
    C --> D[Database Operation]
    D --> E[Supabase Response]
    E --> F[Update Service State]
    F --> G[Emit Stream Event]
    G --> H[UI Rebuild]
    H --> I[Display Updated Data]
```

### State Flow Examples:
- **Temperature Update**: Sensor → Service → Database → Stream → UI
- **Settings Change**: UI → Service → Database → Auto Control → ESP32
- **Manual Control**: UI → Service → ESP32 → Database → UI Feedback

---

## 🎯 16. COMPLETE OPERATION CYCLE

### Daily Operation Flow:
1. **Morning Startup**
   - App launches automatically
   - Services initialize
   - ESP32 connection established
   - Auto control begins

2. **Continuous Monitoring**
   - Temperature readings every 10 seconds
   - Auto control decisions every 5 seconds
   - Database updates in real-time
   - UI refreshes continuously

3. **User Interactions**
   - View current status
   - Adjust temperature settings
   - Check history data
   - Manual device control

4. **Evening Operations**
   - Continued monitoring
   - History logging
   - Performance tracking
   - Error handling

### Error Recovery Flow:
```
Error Detected → Log Error → Attempt Recovery → Notify User → Continue Operation
```

---

## 📱 17. USER EXPERIENCE FLOW

### First Time User:
```
App Install → Login → Device Setup → Settings Configuration → Start Monitoring
```

### Daily User:
```
App Open → Auto Login → View Dashboard → Check Status → Adjust if Needed
```

### Power User:
```
App Open → Check History → Analyze Trends → Optimize Settings → Monitor Performance
```

---

## 🔧 18. MAINTENANCE FLOW

### Automatic Maintenance:
- **Data Cleanup**: Old records removed automatically
- **Connection Health**: ESP32 connectivity monitored
- **Performance Tracking**: Response times logged
- **Error Recovery**: Automatic retry mechanisms

### Manual Maintenance:
- **Settings Review**: User adjusts temperature thresholds
- **Device Check**: ESP32 status verification
- **History Analysis**: Usage pattern review
- **System Updates**: App and firmware updates

---

## 📈 19. PERFORMANCE MONITORING FLOW

```mermaid
graph TD
    A[System Metrics] --> B[Response Times]
    A --> C[Error Rates]
    A --> D[Device Uptime]
    A --> E[Database Performance]

    B --> F[Performance Dashboard]
    C --> F
    D --> F
    E --> F

    F --> G{Performance OK?}
    G -->|Yes| H[Continue Monitoring]
    G -->|No| I[Alert User]
    I --> J[Suggest Actions]
    J --> K[Apply Fixes]
    K --> H
```

### Key Metrics:
- **Response Time**: < 5 seconds for commands
- **Uptime**: > 99% device availability
- **Accuracy**: ±0.5°C temperature precision
- **Reliability**: < 1% failed operations

---

## 🎯 20. INTEGRATION SUMMARY

### Complete System Integration:
```
Physical World ↔ Hardware ↔ Communication ↔ Software ↔ Database ↔ User Interface
```

### Technology Stack Flow:
```
DS18B20 → ESP32 → WiFi → HTTP → Flutter → Supabase → PostgreSQL → Real-time Streams → UI
```

### Business Logic Flow:
```
Temperature Reading → Analysis → Decision → Action → Feedback → Logging → Reporting
```

Alur aplikasi ini menunjukkan bagaimana semua komponen bekerja sama untuk menciptakan sistem IoT monitoring suhu air yang lengkap dan otomatis! 🌊📱🔧
