import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/esp32_device.dart';
import '../models/relay_control.dart';
import '../models/sensor_data.dart';
import 'database_service.dart';

class ESP32Service {
  static final _supabase = Supabase.instance.client;
  static final DatabaseService _databaseService = DatabaseService();

  // Singleton pattern
  static final ESP32Service _instance = ESP32Service._internal();
  factory ESP32Service() => _instance;
  ESP32Service._internal();

  // Stream controllers
  final StreamController<ESP32Device?> _deviceStatusController =
      StreamController<ESP32Device?>.broadcast();
  final StreamController<List<RelayControl>> _relayStatusController =
      StreamController<List<RelayControl>>.broadcast();

  // Current state
  ESP32Device? _currentDevice;
  List<RelayControl> _relayControls = [];
  Timer? _heartbeatTimer;
  Timer? _sensorReadTimer;

  // Getters
  Stream<ESP32Device?> get deviceStatusStream => _deviceStatusController.stream;
  Stream<List<RelayControl>> get relayStatusStream =>
      _relayStatusController.stream;
  ESP32Device? get currentDevice => _currentDevice;
  List<RelayControl> get relayControls => _relayControls;

  /// Initialize ESP32 service
  Future<void> initialize() async {
    try {
      await _loadDeviceInfo();
      await _loadRelayControls();
      _startHeartbeat();
      _startSensorReading();
    } catch (e) {
      debugPrint('Error initializing ESP32 service: $e');
    }
  }

  /// Load device information from database
  Future<void> _loadDeviceInfo() async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId == null) return;

      final response = await _supabase
          .from('esp32_devices')
          .select('*')
          .eq('user_id', userId)
          .limit(1);

      if (response.isNotEmpty) {
        _currentDevice = ESP32Device.fromJson(response.first);
        _deviceStatusController.add(_currentDevice);
      }
    } catch (e) {
      debugPrint('Error loading device info: $e');
    }
  }

  /// Load relay controls from database
  Future<void> _loadRelayControls() async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId == null) return;

      final response = await _supabase
          .from('relay_controls')
          .select('*')
          .eq('user_id', userId);

      _relayControls = response
          .map<RelayControl>((json) => RelayControl.fromJson(json))
          .toList();

      _relayStatusController.add(_relayControls);
    } catch (e) {
      debugPrint('Error loading relay controls: $e');
    }
  }

  /// Register new ESP32 device
  Future<bool> registerDevice({
    required String deviceName,
    required String macAddress,
    String? ipAddress,
    String? firmwareVersion,
  }) async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId == null) return false;

      final deviceData = {
        'device_name': deviceName,
        'user_id': userId,
        'mac_address': macAddress,
        'ip_address': ipAddress,
        'firmware_version': firmwareVersion,
        'is_online': true,
        'last_seen': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('esp32_devices')
          .insert(deviceData)
          .select()
          .single();

      _currentDevice = ESP32Device.fromJson(response);
      _deviceStatusController.add(_currentDevice);

      // Create default relay controls
      await _createDefaultRelayControls(_currentDevice!.deviceId);

      return true;
    } catch (e) {
      debugPrint('Error registering device: $e');
      return false;
    }
  }

  /// Create default relay controls for heater and pump
  Future<void> _createDefaultRelayControls(String deviceId) async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId == null) return;

      final defaultRelays = [
        {
          'device_id': deviceId,
          'user_id': userId,
          'relay_pin': 2, // GPIO 2 untuk heater
          'relay_name': 'Heater Relay',
          'device_type': 'heater',
          'is_active': false,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        {
          'device_id': deviceId,
          'user_id': userId,
          'relay_pin': 4, // GPIO 4 untuk pump
          'relay_name': 'Pump Relay',
          'device_type': 'pump',
          'is_active': false,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      ];

      await _supabase.from('relay_controls').insert(defaultRelays);
      await _loadRelayControls();
    } catch (e) {
      debugPrint('Error creating default relay controls: $e');
    }
  }

  /// Send command to ESP32
  Future<bool> sendCommand({
    required String command,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      if (_currentDevice?.ipAddress == null) {
        debugPrint('Device IP address not available');
        return false;
      }

      final url = 'http://${_currentDevice!.ipAddress}/api/command';
      final body = {
        'command': command,
        'parameters': parameters ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http
          .post(
            Uri.parse(url),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(body),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error sending command to ESP32: $e');
      return false;
    }
  }

  /// Control relay (heater or pump)
  Future<bool> controlRelay({
    required String deviceType,
    required bool activate,
  }) async {
    try {
      final relay = _relayControls.firstWhere(
        (r) => r.deviceType == deviceType,
        orElse: () =>
            throw Exception('Relay not found for device type: $deviceType'),
      );

      // Send command to ESP32
      final success = await sendCommand(
        command: 'control_relay',
        parameters: {
          'pin': relay.relayPin,
          'state': activate ? 'ON' : 'OFF',
          'device_type': deviceType,
        },
      );

      if (success) {
        // Update database
        await _updateRelayStatus(relay.controlId, activate);

        // Update local state
        final updatedRelay = activate ? relay.activate() : relay.deactivate();
        final index =
            _relayControls.indexWhere((r) => r.controlId == relay.controlId);
        if (index != -1) {
          _relayControls[index] = updatedRelay;
          _relayStatusController.add(_relayControls);
        }

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error controlling relay: $e');
      return false;
    }
  }

  /// Update relay status in database
  Future<void> _updateRelayStatus(String controlId, bool isActive) async {
    try {
      await _supabase.from('relay_controls').update({
        'is_active': isActive,
        'last_activated': isActive ? DateTime.now().toIso8601String() : null,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('control_id', controlId);
    } catch (e) {
      debugPrint('Error updating relay status: $e');
    }
  }

  /// Read sensor data from ESP32
  Future<Map<String, dynamic>?> readSensorData() async {
    try {
      if (_currentDevice?.ipAddress == null) return null;

      final url = 'http://${_currentDevice!.ipAddress}/api/sensor';
      final response =
          await http.get(Uri.parse(url)).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }

      return null;
    } catch (e) {
      debugPrint('Error reading sensor data: $e');
      return null;
    }
  }

  /// Start heartbeat to check device status
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer =
        Timer.periodic(const Duration(seconds: 30), (timer) async {
      await _checkDeviceStatus();
    });
  }

  /// Start periodic sensor reading
  void _startSensorReading() {
    _sensorReadTimer?.cancel();
    _sensorReadTimer =
        Timer.periodic(const Duration(seconds: 10), (timer) async {
      await _readAndStoreSensorData();
    });
  }

  /// Check device online status
  Future<void> _checkDeviceStatus() async {
    try {
      if (_currentDevice == null) return;

      final isOnline = await _pingDevice();

      if (_currentDevice!.isOnline != isOnline) {
        _currentDevice = _currentDevice!.updateOnlineStatus(isOnline);
        _deviceStatusController.add(_currentDevice);

        // Update database
        await _supabase.from('esp32_devices').update({
          'is_online': isOnline,
          'last_seen': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('device_id', _currentDevice!.deviceId);
      }
    } catch (e) {
      debugPrint('Error checking device status: $e');
    }
  }

  /// Ping device to check if online
  Future<bool> _pingDevice() async {
    try {
      if (_currentDevice?.ipAddress == null) return false;

      final url = 'http://${_currentDevice!.ipAddress}/api/ping';
      final response =
          await http.get(Uri.parse(url)).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Read and store sensor data
  Future<void> _readAndStoreSensorData() async {
    try {
      final sensorData = await readSensorData();
      if (sensorData == null) return;

      final userId = _databaseService.currentUserId;
      if (userId == null) return;

      // Create SensorData object
      final data = SensorData(
        suhuAir: sensorData['temperature']?.toString() ?? '0.0',
        humidity: sensorData['humidity']?.toString() ?? '0.0',
        waterLevel: sensorData['water_level']?.toString() ?? '0.0',
        tanggalWaktu: DateTime.now(),
        userId: userId,
        dataId: 'esp32_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
      );

      // Store in database
      await _databaseService.insertSensorData(data);
    } catch (e) {
      debugPrint('Error reading and storing sensor data: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _heartbeatTimer?.cancel();
    _sensorReadTimer?.cancel();
    _deviceStatusController.close();
    _relayStatusController.close();
  }
}
