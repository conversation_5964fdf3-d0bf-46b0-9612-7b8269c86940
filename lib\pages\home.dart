import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../models/sensor_data.dart';
import '../models/temperature_setting.dart';
import '../models/heater_status.dart';
import '../models/heater_history.dart';
import '../services/sensor_service.dart';
import '../services/heater_service.dart';
import '../services/database_service.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // Services
  final SensorService _sensorService = SensorService();
  final HeaterService _heaterService = HeaterService();
  final DatabaseService _databaseService = DatabaseService();

  // State variables
  SensorData? _currentSensorData;
  TemperatureSetting? _temperatureSetting;
  HeaterStatus? _heaterStatus;
  bool _isLoading = true;
  List<FlSpot> _chartData = [];
  double _maxY = 0;
  List<HeaterHistory> _heaterHistory = [];

  // Stream subscriptions
  StreamSubscription<SensorData?>? _sensorDataSubscription;
  StreamSubscription<HeaterStatus?>? _heaterStatusSubscription;
  StreamSubscription<TemperatureStatus>? _temperatureStatusSubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  @override
  void dispose() {
    _sensorDataSubscription?.cancel();
    _heaterStatusSubscription?.cancel();
    _temperatureStatusSubscription?.cancel();
    super.dispose();
  }

  /// Initialize all services and setup streams
  Future<void> _initializeServices() async {
    try {
      setState(() => _isLoading = true);

      // Initialize services
      await _sensorService.initialize();
      await _heaterService.initialize();

      // Setup stream subscriptions
      _setupStreamSubscriptions();

      // Load initial data
      await _loadInitialData();

      // Start data simulation for testing (optional)
      // _sensorService.startDataSimulation();
    } catch (e) {
      debugPrint('Error initializing services: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Setup stream subscriptions for real-time updates
  void _setupStreamSubscriptions() {
    // Listen to sensor data updates
    _sensorDataSubscription = _sensorService.latestDataStream.listen(
      (sensorData) {
        if (mounted && sensorData != null) {
          setState(() {
            _currentSensorData = sensorData;
          });
        }
      },
    );

    // Listen to heater status updates
    _heaterStatusSubscription = _heaterService.heaterStatusStream.listen(
      (heaterStatus) {
        if (mounted && heaterStatus != null) {
          setState(() {
            _heaterStatus = heaterStatus;
          });
        }
      },
    );

    // Listen to temperature status changes
    _temperatureStatusSubscription =
        _sensorService.temperatureStatusStream.listen(
      (temperatureStatus) {
        if (mounted) {
          // Handle temperature status changes (e.g., show notifications)
          _handleTemperatureStatusChange(temperatureStatus);
        }
      },
    );
  }

  /// Handle temperature status changes
  void _handleTemperatureStatusChange(TemperatureStatus status) {
    String message = '';
    Color backgroundColor = Colors.blue;

    switch (status) {
      case TemperatureStatus.tooLow:
        message = 'Suhu air terlalu rendah!';
        backgroundColor = Colors.blue;
        break;
      case TemperatureStatus.tooHigh:
        message = 'Suhu air terlalu tinggi!';
        backgroundColor = Colors.red;
        break;
      case TemperatureStatus.normal:
        // Don't show notification for normal temperature
        return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Load initial data from services
  Future<void> _loadInitialData() async {
    try {
      // Load temperature setting
      final userId = _databaseService.currentUserId;
      if (userId != null) {
        _temperatureSetting =
            await _databaseService.getTemperatureSetting(userId);
      }

      // Load latest sensor data
      _currentSensorData = await _sensorService.getLatestSensorData();

      // Load heater status
      _heaterStatus = _heaterService.currentHeaterStatus;

      // Load heater history for chart
      await _loadHeaterHistory();

      if (mounted) {
        setState(() {
          // Data will be updated through streams
        });
      }
    } catch (e) {
      debugPrint('Error loading initial data: $e');
    }
  }

  /// Generate sample heater history for testing
  List<HeaterHistory> _generateSampleHistory() {
    final List<HeaterHistory> history = [];
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 7));
    final userId = _databaseService.currentUserId ?? 'sample_user';

    // Generate one record per day
    for (var i = 0; i < 7; i++) {
      final currentDate = startDate.add(Duration(days: i));
      final recordDate = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
        12, // Set to noon
        0,
      );

      // Generate random usage time between 30-210 minutes
      final usageMinutes = 30 + (DateTime.now().millisecondsSinceEpoch % 180);

      history.add(HeaterHistory(
        userId: userId,
        statusId: 'sample_status_$i',
        tanggal: recordDate,
        totalWaktuMenyala: usageMinutes,
        heaterId: 'sample_heater_1',
        createdAt: recordDate,
      ));
    }

    // Sort by date ascending for chart
    history.sort((a, b) => a.tanggal.compareTo(b.tanggal));

    return history;
  }

  /// Load heater history for chart
  Future<void> _loadHeaterHistory() async {
    try {
      // Load heater history from service
      final histories = await _heaterService.getHeaterHistory(
        startDate: DateTime.now().subtract(const Duration(days: 7)),
        endDate: DateTime.now(),
        limit: 7,
      );

      // Process data for chart
      _chartData = [];
      _maxY = 0;

      if (histories.isNotEmpty) {
        for (var i = 0; i < histories.length; i++) {
          final history = histories[i];
          final minutes = history.totalWaktuMenyala.toDouble();
          _chartData.add(FlSpot(i.toDouble(), minutes));
          if (minutes > _maxY) _maxY = minutes;
        }

        _heaterHistory = histories;
      } else {
        // Generate sample data if no history exists
        _heaterHistory = _generateSampleHistory();
        for (var i = 0; i < _heaterHistory.length; i++) {
          final history = _heaterHistory[i];
          final minutes = history.totalWaktuMenyala.toDouble();
          _chartData.add(FlSpot(i.toDouble(), minutes));
          if (minutes > _maxY) _maxY = minutes;
        }
      }

      debugPrint('Loaded ${_heaterHistory.length} heater history records');
    } catch (e) {
      debugPrint('Error loading heater history: $e');
      // Generate sample data as fallback
      _heaterHistory = _generateSampleHistory();
      _chartData = [];
      _maxY = 0;

      for (var i = 0; i < _heaterHistory.length; i++) {
        final history = _heaterHistory[i];
        final minutes = history.totalWaktuMenyala.toDouble();
        _chartData.add(FlSpot(i.toDouble(), minutes));
        if (minutes > _maxY) _maxY = minutes;
      }
    }
  }

  /// Get temperature status based on current settings
  String _getTemperatureStatus(double temp) {
    if (_temperatureSetting == null) return 'Tidak diketahui';

    final status = _temperatureSetting!.getTemperatureStatus(temp);
    return status.displayName;
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Terlalu Dingin':
        return Colors.blue;
      case 'Terlalu Panas':
        return Colors.red;
      case 'Normal':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// Toggle heater status using service
  Future<void> _toggleHeaterStatus() async {
    try {
      final success = await _heaterService.toggleHeaterStatus();

      if (mounted) {
        if (success) {
          final newStatus = _heaterService.isHeaterOn;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Status pemanas berhasil diubah ke ${newStatus ? 'ON' : 'OFF'}'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Gagal mengubah status pemanas'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error toggling heater status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal mengubah status pemanas: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Statistik Penggunaan',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '7 Hari Terakhir',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  drawHorizontalLine: true,
                  horizontalInterval: 60, // Interval 60 menit
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withOpacity(0.2),
                      strokeWidth: 1,
                    );
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withOpacity(0.2),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  leftTitles: AxisTitles(
                    axisNameWidget: const Text(
                      'Jam',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 60,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          (value / 60).toInt().toString(),
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        );
                      },
                      reservedSize: 35,
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    axisNameWidget: const Text(
                      'Tanggal',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        if (value.toInt() >= _heaterHistory.length) {
                          return const Text('');
                        }
                        final date = DateTime.parse(
                            _heaterHistory[value.toInt()]['tanggal']);
                        return Transform.rotate(
                          angle: -0.5, // Rotasi 30 derajat
                          child: Text(
                            DateFormat('d/M').format(date),
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 11,
                            ),
                          ),
                        );
                      },
                      reservedSize: 32,
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.2),
                  ),
                ),
                lineBarsData: [
                  LineChartBarData(
                    spots: _chartData,
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 2.5,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 5,
                          color: Colors.white,
                          strokeWidth: 2,
                          strokeColor: Colors.blue,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue.withOpacity(0.3),
                          Colors.blue.withOpacity(0.0),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
                minY: 0,
                maxY: _maxY + (_maxY * 0.1),
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    fitInsideHorizontally: true,
                    fitInsideVertically: true,
                    tooltipRoundedRadius: 8,
                    tooltipPadding: const EdgeInsets.all(8),
                    tooltipBorder: const BorderSide(
                      color: Colors.blueAccent,
                      width: 1,
                    ),
                    getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                      return touchedBarSpots.map((barSpot) {
                        final date = DateTime.parse(
                            _heaterHistory[barSpot.x.toInt()]['tanggal']);
                        final minutes = barSpot.y.toInt();
                        final hours = (minutes / 60).floor();
                        final remainingMinutes = minutes % 60;
                        return LineTooltipItem(
                          '${DateFormat('dd/MM').format(date)}\n${hours}j ${remainingMinutes}m',
                          const TextStyle(
                            color: Colors.black87,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList();
                    },
                  ),
                  handleBuiltInTouches: true,
                  getTouchedSpotIndicator:
                      (LineChartBarData barData, List<int> spotIndexes) {
                    return spotIndexes.map((spotIndex) {
                      return TouchedSpotIndicatorData(
                        FlLine(
                          color: Colors.blue.withOpacity(0.3),
                          strokeWidth: 2,
                          dashArray: [5, 5],
                        ),
                        FlDotData(
                          getDotPainter: (spot, percent, barData, index) {
                            return FlDotCirclePainter(
                              radius: 6,
                              color: Colors.white,
                              strokeWidth: 3,
                              strokeColor: Colors.blue,
                            );
                          },
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF2F5FC),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Image.asset('assets/icons/logo.png', height: 55),
                      const SizedBox(width: 8),
                      const Text(
                        'AQUATEMP',
                        style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            fontFamily: 'Magilio'),
                      ),
                    ],
                  ),
                  const CircleAvatar(
                    radius: 20,
                    backgroundImage: AssetImage('assets/images/milos.webp'),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : StreamBuilder<List<Map<String, dynamic>>>(
                      stream: _tempSettingsStream,
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                          final settings = snapshot.data!.first;
                          _minTemp = double.tryParse(
                              settings['min_temperature']?.toString() ?? '0');
                          _maxTemp = double.tryParse(
                              settings['max_temperature']?.toString() ?? '0');
                        }

                        return Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Row(
                                          children: [
                                            Icon(Icons.water_drop),
                                            SizedBox(width: 8),
                                            Text(
                                              'Suhu Air',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '${_currentTemp?.toStringAsFixed(1) ?? '-'}°C',
                                          style: const TextStyle(
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        Text(
                                          _currentTemp != null
                                              ? _getTemperatureStatus(
                                                  _currentTemp!)
                                              : 'Tidak diketahui',
                                          style: TextStyle(
                                            color: _currentTemp != null
                                                ? _getStatusColor(
                                                    _getTemperatureStatus(
                                                        _currentTemp!))
                                                : Colors.grey,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Container(
                                          height: 30,
                                          decoration: BoxDecoration(
                                            color: _currentTemp != null
                                                ? _getStatusColor(
                                                        _getTemperatureStatus(
                                                            _currentTemp!))
                                                    .withOpacity(0.2)
                                                : Colors.grey.withOpacity(0.2),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Container(
                                  width: 140,
                                  height: 140,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Row(
                                        children: [
                                          Icon(Icons.memory),
                                          SizedBox(width: 4),
                                          Text('Pemanas'),
                                        ],
                                      ),
                                      const Spacer(),
                                      const Text(
                                        'Status',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: GestureDetector(
                                              onTap: _toggleHeaterStatus,
                                              child: Container(
                                                height: 30,
                                                decoration: BoxDecoration(
                                                  color: _isHeaterOn
                                                      ? Colors.green
                                                      : Colors.red,
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                alignment: Alignment.center,
                                                child: Text(
                                                  _isHeaterOn ? 'ON' : 'OFF',
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Row(
                                    children: [
                                      Icon(Icons.show_chart),
                                      SizedBox(width: 8),
                                      Text('Batas Suhu',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Container(
                                    height: 100,
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colors.blue,
                                          Colors.purple,
                                          Colors.red
                                        ],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    alignment: Alignment.center,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12.0),
                                          child: Text(
                                            '${_minTemp?.toStringAsFixed(1) ?? '-'}°C',
                                            style: const TextStyle(
                                                fontSize: 22,
                                                color: Colors.white),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12.0),
                                          child: Text(
                                            '${_maxTemp?.toStringAsFixed(1) ?? '-'}°C',
                                            style: const TextStyle(
                                                fontSize: 22,
                                                color: Colors.white),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            _heaterHistory.isNotEmpty
                                ? _buildStatisticsCard()
                                : const SizedBox(),
                          ],
                        );
                      }),
            ],
          ),
        ),
      ),
    );
  }
}
