<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2916.5 636" style="max-width: 2916.5px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30"><style>#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .error-icon{fill:#a44141;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-thickness-normal{stroke-width:1px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .marker.cross{stroke:lightgrey;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 p{margin:0;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster-label text{fill:#F9FFFE;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster-label span{color:#F9FFFE;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster-label span p{background-color:transparent;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .label text,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 span{fill:#ccc;color:#ccc;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node rect,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node circle,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node ellipse,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node polygon,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .rough-node .label text,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node .label text,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .image-shape .label,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .icon-shape .label{text-anchor:middle;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .rough-node .label,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node .label,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .image-shape .label,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .icon-shape .label{text-align:center;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .node.clickable{cursor:pointer;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .arrowheadPath{fill:lightgrey;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster text{fill:#F9FFFE;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .cluster span{color:#F9FFFE;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 rect.text{fill:none;stroke-width:0;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .icon-shape,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .icon-shape p,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .icon-shape rect,#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .uiClass&gt;*{fill:#E6F3FF!important;stroke:#0066CC!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .uiClass span{fill:#E6F3FF!important;stroke:#0066CC!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .serviceClass&gt;*{fill:#F0FFF0!important;stroke:#228B22!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .serviceClass span{fill:#F0FFF0!important;stroke:#228B22!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .modelClass&gt;*{fill:#FFF8DC!important;stroke:#DAA520!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .modelClass span{fill:#FFF8DC!important;stroke:#DAA520!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .externalClass&gt;*{fill:#FFE4E1!important;stroke:#DC143C!important;stroke-width:2px!important;}#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30 .externalClass span{fill:#FFE4E1!important;stroke:#DC143C!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="External" class="cluster"><rect height="104" width="1037.0859375" y="524" x="29.96875" style=""></rect><g transform="translate(473.75390625, 524)" class="cluster-label"><foreignObject height="24" width="149.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 External Services</p></span></div></foreignObject></g></g><g data-look="classic" id="Models" class="cluster"><rect height="104" width="1821.4453125" y="524" x="1087.0546875" style=""></rect><g transform="translate(1941.15234375, 524)" class="cluster-label"><foreignObject height="24" width="113.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Model Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Services" class="cluster"><rect height="312" width="2770.41015625" y="162" x="68.6796875" style=""></rect><g transform="translate(1392.564453125, 162)" class="cluster-label"><foreignObject height="24" width="122.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Service Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="UI" class="cluster"><rect height="104" width="2885.609375" y="8" x="8" style=""></rect><g transform="translate(1407.9140625, 8)" class="cluster-label"><foreignObject height="24" width="85.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖥️ UI Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_G_0" d="M108.68,87L108.68,91.167C108.68,95.333,108.68,103.667,108.68,112C108.68,120.333,108.68,128.667,108.68,137C108.68,145.333,108.68,153.667,108.68,166.5C108.68,179.333,108.68,196.667,108.68,214C108.68,231.333,108.68,248.667,108.68,266C108.68,283.333,108.68,300.667,108.68,318C108.68,335.333,108.68,352.667,229.167,369.171C349.655,385.675,590.631,401.351,711.118,409.188L831.606,417.026"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_H_1" d="M801.373,87L808.852,91.167C816.331,95.333,831.288,103.667,838.767,112C846.246,120.333,846.246,128.667,846.246,137C846.246,145.333,846.246,153.667,846.246,166.5C846.246,179.333,846.246,196.667,846.246,214C846.246,231.333,846.246,248.667,846.246,266C846.246,283.333,846.246,300.667,846.246,318C846.246,335.333,846.246,352.667,889.815,367.734C933.384,382.801,1020.522,395.602,1064.091,402.003L1107.66,408.404"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_I_2" d="M771.442,87L774.302,91.167C777.162,95.333,782.882,103.667,785.742,112C788.602,120.333,788.602,128.667,788.602,137C788.602,145.333,788.602,153.667,788.602,166.5C788.602,179.333,788.602,196.667,788.602,214C788.602,231.333,788.602,248.667,837.541,264.09C886.481,279.512,984.361,293.025,1033.301,299.781L1082.241,306.537"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_J_3" d="M685.707,72.358L649.779,78.965C613.852,85.572,541.996,98.786,506.068,109.56C470.141,120.333,470.141,128.667,470.141,137C470.141,145.333,470.141,153.667,470.141,166.5C470.141,179.333,470.141,196.667,470.141,214C470.141,231.333,470.141,248.667,470.141,260.833C470.141,273,470.141,280,470.141,283.5L470.141,287"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_H_4" d="M964.774,87L965.576,91.167C966.377,95.333,967.979,103.667,968.781,112C969.582,120.333,969.582,128.667,969.582,137C969.582,145.333,969.582,153.667,969.582,166.5C969.582,179.333,969.582,196.667,969.582,214C969.582,231.333,969.582,248.667,969.582,266C969.582,283.333,969.582,300.667,969.582,318C969.582,335.333,969.582,352.667,992.604,366.524C1015.626,380.382,1061.671,390.763,1084.693,395.954L1107.715,401.145"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_I_5" d="M911.119,87L903.64,91.167C896.161,95.333,881.204,103.667,873.725,112C866.246,120.333,866.246,128.667,866.246,137C866.246,145.333,866.246,153.667,866.246,166.5C866.246,179.333,866.246,196.667,866.246,214C866.246,231.333,866.246,248.667,902.249,263.594C938.251,278.522,1010.257,291.043,1046.26,297.304L1082.262,303.565"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_H_6" d="M1514.551,87L1514.551,91.167C1514.551,95.333,1514.551,103.667,1514.551,112C1514.551,120.333,1514.551,128.667,1514.551,137C1514.551,145.333,1514.551,153.667,1514.551,166.5C1514.551,179.333,1514.551,196.667,1514.551,214C1514.551,231.333,1514.551,248.667,1514.551,266C1514.551,283.333,1514.551,300.667,1514.551,318C1514.551,335.333,1514.551,352.667,1477.584,367.449C1440.618,382.23,1366.684,394.461,1329.718,400.576L1292.751,406.691"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_7" d="M1725.129,87L1728.175,91.167C1731.22,95.333,1737.311,103.667,1740.357,112C1743.402,120.333,1743.402,128.667,1743.402,137C1743.402,145.333,1743.402,153.667,1743.402,166.5C1743.402,179.333,1743.402,196.667,1743.402,214C1743.402,231.333,1743.402,248.667,1743.402,266C1743.402,283.333,1743.402,300.667,1743.402,318C1743.402,335.333,1743.402,352.667,1616.926,369.207C1490.449,385.747,1237.496,401.493,1111.02,409.366L984.543,417.24"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_H_8" d="M1661.041,87L1654.196,91.167C1647.351,95.333,1633.662,103.667,1626.817,112C1619.973,120.333,1619.973,128.667,1619.973,137C1619.973,145.333,1619.973,153.667,1619.973,166.5C1619.973,179.333,1619.973,196.667,1619.973,214C1619.973,231.333,1619.973,248.667,1619.973,266C1619.973,283.333,1619.973,300.667,1619.973,318C1619.973,335.333,1619.973,352.667,1565.44,368.089C1510.907,383.511,1401.84,397.022,1347.307,403.778L1292.774,410.533"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_K_9" d="M2528.828,72.724L2577.205,79.27C2625.582,85.816,2722.336,98.908,2770.713,109.621C2819.09,120.333,2819.09,128.667,2819.09,137C2819.09,145.333,2819.09,153.667,2819.09,166.5C2819.09,179.333,2819.09,196.667,2819.09,214C2819.09,231.333,2819.09,248.667,2712.11,265.031C2605.129,281.395,2391.169,296.79,2284.189,304.487L2177.208,312.185"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_L_10" d="M2340.75,70.449L2278.427,77.374C2216.104,84.3,2091.458,98.15,2029.135,109.242C1966.813,120.333,1966.813,128.667,1966.813,137C1966.813,145.333,1966.813,153.667,1966.813,161.333C1966.813,169,1966.813,176,1966.813,179.5L1966.813,183"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_M_11" d="M930.784,449L934.289,453.167C937.794,457.333,944.803,465.667,948.308,474C951.813,482.333,951.813,490.667,1022.123,499C1092.434,507.333,1233.055,515.667,1329.945,526.438C1426.836,537.21,1479.997,550.42,1506.577,557.025L1533.157,563.63"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_M_12" d="M1227.105,449L1231.256,453.167C1235.406,457.333,1243.707,465.667,1247.857,474C1252.008,482.333,1252.008,490.667,1359.79,499C1467.573,507.333,1683.138,515.667,1746.601,527.132C1810.063,538.597,1721.423,553.194,1677.103,560.493L1632.783,567.792"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_N_13" d="M1215.788,449L1218.192,453.167C1220.596,457.333,1225.403,465.667,1227.807,474C1230.211,482.333,1230.211,490.667,1328.93,499C1427.65,507.333,1625.089,515.667,1773.519,526.783C1921.95,537.9,2021.373,551.8,2071.085,558.75L2120.796,565.7"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_O_14" d="M1205.403,449L1206.205,453.167C1207.006,457.333,1208.608,465.667,1209.41,474C1210.211,482.333,1210.211,490.667,1308.93,499C1407.65,507.333,1605.089,515.667,1702.258,523.389C1799.427,531.111,1796.328,538.222,1794.778,541.778L1793.228,545.333"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_P_15" d="M1195.019,449L1194.217,453.167C1193.416,457.333,1191.814,465.667,1191.012,474C1190.211,482.333,1190.211,490.667,1285.298,499C1380.384,507.333,1570.557,515.667,1620.688,526.51C1670.819,537.353,1580.907,550.706,1535.951,557.382L1490.996,564.058"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_Q_16" d="M1184.634,449L1182.23,453.167C1179.826,457.333,1175.019,465.667,1172.615,474C1170.211,482.333,1170.211,490.667,1249.783,499C1329.355,507.333,1488.5,515.667,1508.149,526.864C1527.798,538.06,1407.952,552.121,1348.029,559.151L1288.106,566.181"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_N_17" d="M1244.344,332.568L1278.206,338.807C1312.068,345.045,1379.792,357.523,1413.654,372.428C1447.516,387.333,1447.516,404.667,1447.516,422C1447.516,439.333,1447.516,456.667,1447.516,469.5C1447.516,482.333,1447.516,490.667,1527.467,499C1607.419,507.333,1767.323,515.667,1879.542,526.112C1991.762,536.557,2056.296,549.114,2088.564,555.393L2120.831,561.672"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_O_18" d="M1119.24,345L1112.137,349.167C1105.033,353.333,1090.825,361.667,1083.721,374.5C1076.617,387.333,1076.617,404.667,1076.617,422C1076.617,439.333,1076.617,456.667,1076.617,469.5C1076.617,482.333,1076.617,490.667,1144.204,499C1211.79,507.333,1346.964,515.667,1446.677,525.445C1546.39,535.222,1610.643,546.445,1642.769,552.056L1674.896,557.667"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_P_19" d="M550.43,332.009L586.718,338.341C623.007,344.673,695.583,357.336,731.872,372.335C768.16,387.333,768.16,404.667,768.16,422C768.16,439.333,768.16,456.667,768.16,469.5C768.16,482.333,768.16,490.667,853.05,499C937.94,507.333,1107.72,515.667,1202.653,523.757C1297.586,531.848,1317.672,539.696,1327.715,543.62L1337.758,547.544"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_Q_20" d="M550.43,340.576L567.87,345.48C585.311,350.384,620.193,360.192,637.633,373.763C655.074,387.333,655.074,404.667,655.074,422C655.074,439.333,655.074,456.667,655.074,469.5C655.074,482.333,655.074,490.667,739.964,499C824.854,507.333,994.634,515.667,1082.323,523.472C1170.011,531.277,1175.609,538.553,1178.407,542.191L1181.206,545.83"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_R_21" d="M2173.219,325.135L2253.73,332.613C2334.241,340.09,2495.263,355.045,2575.774,371.189C2656.285,387.333,2656.285,404.667,2656.285,422C2656.285,439.333,2656.285,456.667,2656.285,469.5C2656.285,482.333,2656.285,490.667,2656.285,499C2656.285,507.333,2656.285,515.667,2667.072,523.771C2677.859,531.876,2699.432,539.752,2710.219,543.69L2721.005,547.628"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_S_22" d="M2173.219,329.112L2220.335,335.927C2267.452,342.741,2361.685,356.371,2408.801,371.852C2455.918,387.333,2455.918,404.667,2455.918,422C2455.918,439.333,2455.918,456.667,2455.918,469.5C2455.918,482.333,2455.918,490.667,2455.918,499C2455.918,507.333,2455.918,515.667,2466.705,523.771C2477.491,531.876,2499.065,539.752,2509.852,543.69L2520.638,547.628"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_N_23" d="M2065.594,220.625L2178.361,228.187C2291.129,235.75,2516.664,250.875,2629.432,267.104C2742.199,283.333,2742.199,300.667,2742.199,318C2742.199,335.333,2742.199,352.667,2742.199,370C2742.199,387.333,2742.199,404.667,2742.199,422C2742.199,439.333,2742.199,456.667,2742.199,469.5C2742.199,482.333,2742.199,490.667,2742.199,499C2742.199,507.333,2742.199,515.667,2663.193,527.334C2584.187,539.001,2426.174,554.003,2347.168,561.503L2268.162,569.004"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_O_24" d="M2065.594,220.8L2175.028,228.333C2284.462,235.867,2503.331,250.933,2612.765,267.133C2722.199,283.333,2722.199,300.667,2722.199,318C2722.199,335.333,2722.199,352.667,2722.199,370C2722.199,387.333,2722.199,404.667,2722.199,422C2722.199,439.333,2722.199,456.667,2722.199,469.5C2722.199,482.333,2722.199,490.667,2722.199,499C2722.199,507.333,2722.199,515.667,2582.645,527.534C2443.092,539.402,2163.984,554.803,2024.43,562.504L1884.877,570.205"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_T_25" d="M1932.274,241L1926.943,245.167C1921.613,249.333,1910.953,257.667,1905.623,270.5C1900.293,283.333,1900.293,300.667,1900.293,318C1900.293,335.333,1900.293,352.667,1900.293,370C1900.293,387.333,1900.293,404.667,1900.293,422C1900.293,439.333,1900.293,456.667,1900.293,469.5C1900.293,482.333,1900.293,490.667,1975.245,499C2050.197,507.333,2200.1,515.667,2229.837,526.605C2259.574,537.544,2169.144,551.089,2123.929,557.861L2078.714,564.633"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_U_26" d="M868.521,449L862.418,453.167C856.314,457.333,844.106,465.667,838.002,474C831.898,482.333,831.898,490.667,748.231,499C664.564,507.333,497.229,515.667,397.296,524.451C297.363,533.235,264.832,542.469,248.567,547.086L232.301,551.704"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_V_27" d="M1173.316,449L1169.166,453.167C1165.016,457.333,1156.715,465.667,1152.564,474C1148.414,482.333,1148.414,490.667,1037.004,499C925.594,507.333,702.773,515.667,612.288,524.611C521.802,533.555,563.65,543.11,584.575,547.888L605.499,552.666"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_V_28" d="M1108.856,345L1100.149,349.167C1091.443,353.333,1074.03,361.667,1065.324,374.5C1056.617,387.333,1056.617,404.667,1056.617,422C1056.617,439.333,1056.617,456.667,1056.617,469.5C1056.617,482.333,1056.617,490.667,951.001,499C845.384,507.333,634.151,515.667,558.959,525.389C483.766,535.111,544.615,546.222,575.039,551.777L605.463,557.333"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_V_29" d="M389.852,337.116L366.831,342.596C343.811,348.077,297.771,359.039,274.751,373.186C251.73,387.333,251.73,404.667,251.73,422C251.73,439.333,251.73,456.667,251.73,469.5C251.73,482.333,251.73,490.667,251.73,499C251.73,507.333,251.73,515.667,310.679,526.556C369.628,537.446,487.526,550.891,546.475,557.614L605.424,564.337"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_W_30" d="M2049.565,345L2042.338,349.167C2035.112,353.333,2020.66,361.667,2013.433,374.5C2006.207,387.333,2006.207,404.667,2006.207,422C2006.207,439.333,2006.207,456.667,2006.207,469.5C2006.207,482.333,2006.207,490.667,1829.176,499C1652.146,507.333,1298.085,515.667,1121.054,523.333C944.023,531,944.023,538,944.023,541.5L944.023,545"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_V_31" d="M2039.18,345L2030.351,349.167C2021.522,353.333,2003.865,361.667,1995.036,374.5C1986.207,387.333,1986.207,404.667,1986.207,422C1986.207,439.333,1986.207,456.667,1986.207,469.5C1986.207,482.333,1986.207,490.667,1791.149,499C1596.091,507.333,1205.975,515.667,1002.851,523.711C799.727,531.756,783.595,539.511,775.529,543.389L767.462,547.267"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_V_32" d="M1921.889,241L1914.956,245.167C1908.024,249.333,1894.158,257.667,1887.226,270.5C1880.293,283.333,1880.293,300.667,1880.293,318C1880.293,335.333,1880.293,352.667,1880.293,370C1880.293,387.333,1880.293,404.667,1880.293,422C1880.293,439.333,1880.293,456.667,1880.293,469.5C1880.293,482.333,1880.293,490.667,1696.221,499C1512.148,507.333,1144.004,515.667,955,523.596C765.996,531.525,756.132,539.049,751.2,542.812L746.268,546.574"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_H_33" d="M1098.471,345L1088.162,349.167C1077.853,353.333,1057.235,361.667,1059.399,369.798C1061.563,377.929,1086.51,385.859,1098.983,389.824L1111.456,393.788"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_H_34" d="M389.852,334.057L359.896,340.047C329.941,346.038,270.031,358.019,389.66,371.866C509.288,385.712,808.455,401.425,958.039,409.281L1107.623,417.137"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_H_35" d="M2028.795,345L2018.364,349.167C2007.933,353.333,1987.07,361.667,1864.403,373.452C1741.737,385.238,1517.266,400.477,1405.031,408.096L1292.796,415.715"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_I_36" d="M1911.504,241L1902.969,245.167C1894.434,249.333,1877.363,257.667,1766.835,269.464C1656.306,281.262,1452.319,296.524,1350.326,304.155L1248.333,311.786"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_K_37" d="M1901.12,241L1890.982,245.167C1880.844,249.333,1860.569,257.667,1879.656,267.767C1898.743,277.868,1957.193,289.736,1986.418,295.67L2015.642,301.604"></path><path marker-end="url(#mermaid-34a392cc-1cfd-4e15-a1bf-1ced90ebbd30_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_H_38" d="M1890.735,241L1878.995,245.167C1867.254,249.333,1843.774,257.667,1832.033,270.5C1820.293,283.333,1820.293,300.667,1820.293,318C1820.293,335.333,1820.293,352.667,1732.376,368.706C1644.459,384.745,1468.625,399.491,1380.708,406.864L1292.791,414.236"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(108.6796875, 60)" id="flowchart-A-246" class="node default uiClass"><rect height="54" width="131.359375" y="-27" x="-65.6796875" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-35.6796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="71.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LoginPage</p></span></div></foreignObject></g></g><g transform="translate(752.91015625, 60)" id="flowchart-B-247" class="node default uiClass"><rect height="54" width="134.40625" y="-27" x="-67.203125" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-37.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="74.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HomePage</p></span></div></foreignObject></g></g><g transform="translate(959.58203125, 60)" id="flowchart-C-248" class="node default uiClass"><rect height="54" width="178.9375" y="-27" x="-89.46875" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-59.46875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="118.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UpdateSuhuPage</p></span></div></foreignObject></g></g><g transform="translate(1514.55078125, 60)" id="flowchart-D-249" class="node default uiClass"><rect height="54" width="150.171875" y="-27" x="-75.0859375" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.0859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RiwayatPage</p></span></div></foreignObject></g></g><g transform="translate(1705.39453125, 60)" id="flowchart-E-250" class="node default uiClass"><rect height="54" width="131.515625" y="-27" x="-65.7578125" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-35.7578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="71.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ProfilPage</p></span></div></foreignObject></g></g><g transform="translate(2434.7890625, 60)" id="flowchart-F-251" class="node default uiClass"><rect height="54" width="188.078125" y="-27" x="-94.0390625" style="fill:#E6F3FF !important;stroke:#0066CC !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64.0390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ESP32ControlPage</p></span></div></foreignObject></g></g><g transform="translate(908.07421875, 422)" id="flowchart-G-252" class="node default serviceClass"><rect height="54" width="144.953125" y="-27" x="-72.4765625" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-42.4765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AuthService</p></span></div></foreignObject></g></g><g transform="translate(1200.2109375, 422)" id="flowchart-H-253" class="node default serviceClass"><rect height="54" width="177.1875" y="-27" x="-88.59375" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-58.59375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DatabaseService</p></span></div></foreignObject></g></g><g transform="translate(1165.2734375, 318)" id="flowchart-I-254" class="node default serviceClass"><rect height="54" width="158.140625" y="-27" x="-79.0703125" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.0703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SensorService</p></span></div></foreignObject></g></g><g transform="translate(470.140625, 318)" id="flowchart-J-255" class="node default serviceClass"><rect height="54" width="160.578125" y="-27" x="-80.2890625" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-50.2890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HeaterService</p></span></div></foreignObject></g></g><g transform="translate(2096.390625, 318)" id="flowchart-K-256" class="node default serviceClass"><rect height="54" width="153.65625" y="-27" x="-76.828125" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="93.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ESP32Service</p></span></div></foreignObject></g></g><g transform="translate(1966.8125, 214)" id="flowchart-L-257" class="node default serviceClass"><rect height="54" width="197.5625" y="-27" x="-98.78125" style="fill:#F0FFF0 !important;stroke:#228B22 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="137.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AutoControlService</p></span></div></foreignObject></g></g><g transform="translate(1582.9375, 576)" id="flowchart-M-258" class="node default modelClass"><rect height="54" width="91.796875" y="-27" x="-45.8984375" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-15.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="31.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User</p></span></div></foreignObject></g></g><g transform="translate(2194.46875, 576)" id="flowchart-N-259" class="node default modelClass"><rect height="54" width="139.421875" y="-27" x="-69.7109375" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-39.7109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="79.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SensorData</p></span></div></foreignObject></g></g><g transform="translate(1779.859375, 576)" id="flowchart-O-260" class="node default modelClass"><rect height="54" width="202.046875" y="-27" x="-101.0234375" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="142.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TemperatureSetting</p></span></div></foreignObject></g></g><g transform="translate(1410.5859375, 576)" id="flowchart-P-261" class="node default modelClass"><rect height="54" width="152.90625" y="-27" x="-76.453125" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="92.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HeaterStatus</p></span></div></foreignObject></g></g><g transform="translate(1204.4140625, 576)" id="flowchart-Q-262" class="node default modelClass"><rect height="54" width="159.4375" y="-27" x="-79.71875" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.71875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HeaterHistory</p></span></div></foreignObject></g></g><g transform="translate(2798.71875, 576)" id="flowchart-R-263" class="node default modelClass"><rect height="54" width="149.5625" y="-27" x="-74.78125" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-44.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="89.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ESP32Device</p></span></div></foreignObject></g></g><g transform="translate(2598.3515625, 576)" id="flowchart-S-264" class="node default modelClass"><rect height="54" width="151.171875" y="-27" x="-75.5859375" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.5859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="91.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RelayControl</p></span></div></foreignObject></g></g><g transform="translate(2002.8203125, 576)" id="flowchart-T-265" class="node default modelClass"><rect height="54" width="143.875" y="-27" x="-71.9375" style="fill:#FFF8DC !important;stroke:#DAA520 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PumpStatus</p></span></div></foreignObject></g></g><g transform="translate(146.7109375, 576)" id="flowchart-U-266" class="node default externalClass"><rect height="54" width="163.484375" y="-27" x="-81.7421875" style="fill:#FFE4E1 !important;stroke:#DC143C !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.7421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="103.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Supabase Auth</p></span></div></foreignObject></g></g><g transform="translate(707.6953125, 576)" id="flowchart-V-267" class="node default externalClass"><rect height="54" width="196.59375" y="-27" x="-98.296875" style="fill:#FFE4E1 !important;stroke:#DC143C !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Supabase Database</p></span></div></foreignObject></g></g><g transform="translate(944.0234375, 576)" id="flowchart-W-268" class="node default externalClass"><rect height="54" width="176.0625" y="-27" x="-88.03125" style="fill:#FFE4E1 !important;stroke:#DC143C !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-58.03125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ESP32 Hardware</p></span></div></foreignObject></g></g></g></g></g></svg>