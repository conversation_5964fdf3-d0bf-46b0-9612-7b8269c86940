import 'dart:async';
import 'package:flutter/material.dart';
import '../models/heater_status.dart';
import '../models/heater_history.dart';
import '../models/temperature_setting.dart';
import 'database_service.dart';
import 'sensor_service.dart';

class HeaterService {
  final DatabaseService _databaseService = DatabaseService();
  final SensorService _sensorService = SensorService();

  // Singleton pattern
  static final HeaterService _instance = HeaterService._internal();
  factory HeaterService() => _instance;
  HeaterService._internal();

  // Stream controllers
  final StreamController<HeaterStatus?> _heaterStatusController =
      StreamController<HeaterStatus?>.broadcast();
  final StreamController<bool> _autoModeController =
      StreamController<bool>.broadcast();

  // Getters untuk streams
  Stream<HeaterStatus?> get heaterStatusStream =>
      _heaterStatusController.stream;
  Stream<bool> get autoModeStream => _autoModeController.stream;

  // State variables
  HeaterStatus? _currentHeaterStatus;
  bool _isAutoModeEnabled = false;
  Timer? _autoControlTimer;
  Timer? _usageTrackingTimer;
  DateTime? _heaterStartTime;

  /// Initialize heater service
  Future<void> initialize() async {
    try {
      // Load current heater status
      await _loadCurrentHeaterStatus();

      // Start real-time monitoring
      _startRealTimeMonitoring();

      // Start usage tracking
      _startUsageTracking();

      debugPrint('HeaterService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing HeaterService: $e');
    }
  }

  /// Load status heater saat ini
  Future<void> _loadCurrentHeaterStatus() async {
    try {
      _currentHeaterStatus = await _databaseService.getLatestHeaterStatus();

      if (_currentHeaterStatus != null) {
        _heaterStatusController.add(_currentHeaterStatus);

        // Set start time jika heater sedang on
        if (_currentHeaterStatus!.isOn) {
          _heaterStartTime = _currentHeaterStatus!.updatedAt ?? DateTime.now();
        }
      }
    } catch (e) {
      debugPrint('Error loading heater status: $e');
    }
  }

  /// Start real-time monitoring
  void _startRealTimeMonitoring() {
    // Monitor heater status stream dari database
    _databaseService.getHeaterStatusStream().listen(
      (heaterStatus) {
        if (heaterStatus != null) {
          final wasOn = _currentHeaterStatus?.isOn ?? false;
          _currentHeaterStatus = heaterStatus;
          _heaterStatusController.add(heaterStatus);

          // Track usage time
          if (!wasOn && heaterStatus.isOn) {
            _heaterStartTime = DateTime.now();
          } else if (wasOn && !heaterStatus.isOn) {
            _recordUsageTime();
          }
        }
      },
      onError: (error) {
        debugPrint('Error in heater status stream: $error');
      },
    );

    // Monitor temperature untuk auto control
    _sensorService.temperatureStatusStream.listen(
      (temperatureStatus) {
        if (_isAutoModeEnabled) {
          _handleAutoControl(temperatureStatus);
        }
      },
    );
  }

  /// Start usage tracking timer
  void _startUsageTracking() {
    _usageTrackingTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_currentHeaterStatus?.isOn == true && _heaterStartTime != null) {
        // Update usage setiap menit jika heater masih on
        // Ini bisa digunakan untuk real-time tracking
      }
    });
  }

  /// Record usage time ke database
  Future<void> _recordUsageTime() async {
    if (_heaterStartTime == null || _currentHeaterStatus == null) return;

    try {
      final endTime = DateTime.now();
      final usageMinutes = endTime.difference(_heaterStartTime!).inMinutes;

      if (usageMinutes > 0) {
        final userId = _databaseService.currentUserId;
        if (userId != null) {
          final history = HeaterHistory(
            userId: userId,
            statusId: _currentHeaterStatus!.statusId,
            tanggal: DateTime.now(),
            totalWaktuMenyala: usageMinutes,
            heaterId: _currentHeaterStatus!.sensorId,
            createdAt: DateTime.now(),
          );

          await _databaseService.insertHeaterHistory(history);
          debugPrint('Recorded heater usage: $usageMinutes minutes');
        }
      }

      _heaterStartTime = null;
    } catch (e) {
      debugPrint('Error recording usage time: $e');
    }
  }

  /// Handle auto control berdasarkan suhu
  Future<void> _handleAutoControl(TemperatureStatus temperatureStatus) async {
    if (!_isAutoModeEnabled || _currentHeaterStatus == null) return;

    try {
      bool shouldTurnOn = false;

      switch (temperatureStatus) {
        case TemperatureStatus.tooLow:
          shouldTurnOn = true;
          break;
        case TemperatureStatus.normal:
        case TemperatureStatus.tooHigh:
          shouldTurnOn = false;
          break;
      }

      // Update status jika berbeda
      if (_currentHeaterStatus!.isOn != shouldTurnOn) {
        await setHeaterStatus(shouldTurnOn, isAutomatic: true);

        final action = shouldTurnOn ? 'ON' : 'OFF';
        debugPrint(
            'Auto control: Heater turned $action due to ${temperatureStatus.displayName}');
      }
    } catch (e) {
      debugPrint('Error in auto control: $e');
    }
  }

  /// Set status heater (manual atau otomatis)
  Future<bool> setHeaterStatus(bool turnOn, {bool isAutomatic = false}) async {
    try {
      if (_currentHeaterStatus == null) {
        debugPrint('No heater status available');
        return false;
      }

      final newStatus = turnOn ? 'ON' : 'OFF';

      // Update di database
      final updatedStatus = await _databaseService.updateHeaterStatus(
        statusId: _currentHeaterStatus!.statusId,
        status: newStatus,
        sensorId: _currentHeaterStatus!.sensorId,
      );

      if (updatedStatus != null) {
        // Record usage time jika heater dimatikan
        if (_currentHeaterStatus!.isOn && !turnOn) {
          await _recordUsageTime();
        }

        // Set start time jika heater dinyalakan
        if (!_currentHeaterStatus!.isOn && turnOn) {
          _heaterStartTime = DateTime.now();
        }

        _currentHeaterStatus = updatedStatus;
        _heaterStatusController.add(updatedStatus);

        final source = isAutomatic ? 'automatic' : 'manual';
        debugPrint('Heater status updated ($source): $newStatus');

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error setting heater status: $e');
      return false;
    }
  }

  /// Toggle heater status
  Future<bool> toggleHeaterStatus() async {
    if (_currentHeaterStatus == null) return false;
    return await setHeaterStatus(!_currentHeaterStatus!.isOn);
  }

  /// Enable/disable auto mode
  Future<void> setAutoMode(bool enabled) async {
    try {
      _isAutoModeEnabled = enabled;
      _autoModeController.add(enabled);

      if (enabled) {
        // Start auto control timer
        _autoControlTimer =
            Timer.periodic(const Duration(seconds: 30), (timer) {
          // Auto control akan dijalankan melalui temperature status stream
        });

        debugPrint('Auto mode enabled');
      } else {
        // Stop auto control timer
        _autoControlTimer?.cancel();
        _autoControlTimer = null;

        debugPrint('Auto mode disabled');
      }
    } catch (e) {
      debugPrint('Error setting auto mode: $e');
    }
  }

  /// Mendapatkan riwayat penggunaan heater
  Future<List<HeaterHistory>> getHeaterHistory({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final userId = _databaseService.currentUserId;
      return await _databaseService.getHeaterHistory(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      debugPrint('Error getting heater history: $e');
      return [];
    }
  }

  /// Mendapatkan statistik penggunaan heater
  Future<HeaterUsageStats> getUsageStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Default ke data 30 hari terakhir
      startDate ??= DateTime.now().subtract(const Duration(days: 30));
      endDate ??= DateTime.now();

      final histories = await getHeaterHistory(
        startDate: startDate,
        endDate: endDate,
      );

      return HeaterUsageStats.fromHistoryList(histories);
    } catch (e) {
      debugPrint('Error getting usage statistics: $e');
      return HeaterUsageStats.fromHistoryList([]);
    }
  }

  /// Mendapatkan penggunaan hari ini
  Future<int> getTodayUsageMinutes() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      final histories = await getHeaterHistory(
        startDate: startOfDay,
        endDate: endOfDay,
      );

      int totalMinutes = 0;
      for (final history in histories) {
        totalMinutes += history.totalWaktuMenyala;
      }

      // Tambahkan waktu saat ini jika heater sedang on
      if (_currentHeaterStatus?.isOn == true && _heaterStartTime != null) {
        final currentUsage =
            DateTime.now().difference(_heaterStartTime!).inMinutes;
        totalMinutes += currentUsage;
      }

      return totalMinutes;
    } catch (e) {
      debugPrint('Error getting today usage: $e');
      return 0;
    }
  }

  /// Estimasi biaya listrik (contoh)
  Future<double> estimateElectricityCost({
    int? usageMinutes,
    double heaterWattage = 1000.0, // Default 1000W
    double electricityRate = 1500.0, // Rupiah per kWh
  }) async {
    try {
      usageMinutes ??= await getTodayUsageMinutes();

      final usageHours = usageMinutes / 60.0;
      final energyKwh = (heaterWattage / 1000.0) * usageHours;
      final cost = energyKwh * electricityRate;

      return cost;
    } catch (e) {
      debugPrint('Error estimating electricity cost: $e');
      return 0.0;
    }
  }

  /// Getters
  HeaterStatus? get currentHeaterStatus => _currentHeaterStatus;
  bool get isAutoModeEnabled => _isAutoModeEnabled;
  bool get isHeaterOn => _currentHeaterStatus?.isOn ?? false;

  /// Get current usage time (jika heater sedang on)
  Duration? get currentUsageTime {
    if (_currentHeaterStatus?.isOn == true && _heaterStartTime != null) {
      return DateTime.now().difference(_heaterStartTime!);
    }
    return null;
  }

  /// Dispose resources
  void dispose() {
    _heaterStatusController.close();
    _autoModeController.close();
    _autoControlTimer?.cancel();
    _usageTrackingTimer?.cancel();

    // Record final usage time jika heater masih on
    if (_currentHeaterStatus?.isOn == true) {
      _recordUsageTime();
    }
  }
}
