import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Utility untuk membuat akun admin
/// Hanya digunakan untuk development/testing
class CreateAdminAccount {
  static final _supabase = Supabase.instance.client;

  /// Membuat akun admin dengan email dan password
  static Future<Map<String, dynamic>> createAdmin({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      // 1. Buat akun di Supabase Auth
      final authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
        },
      );

      if (authResponse.user == null) {
        return {
          'success': false,
          'message': 'Gagal membuat akun auth',
        };
      }

      final user = authResponse.user!;

      // 2. Buat data user di tabel users
      await _supabase.from('users').insert({
        'id': user.id,
        'nama_lengkap': fullName,
        'profile_image_url': null,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // 3. Buat data employee (opsional)
      await _supabase.from('employees').insert({
        'user_id': user.id,
        'nama': fullName,
        'posisi': 'Administrator',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // 4. Buat pengaturan suhu default
      await _supabase.from('temperature_settings').insert({
        'user_id': user.id,
        'min_temperature': 25.0,
        'max_temperature': 30.0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'message': 'Akun admin berhasil dibuat',
        'user_id': user.id,
        'email': email,
      };
    } catch (e) {
      debugPrint('Error creating admin account: $e');
      return {
        'success': false,
        'message': 'Error: ${e.toString()}',
      };
    }
  }

  /// Membuat beberapa akun demo
  static Future<List<Map<String, dynamic>>> createDemoAccounts() async {
    final accounts = [
      {
        'email': '<EMAIL>',
        'password': 'admin123',
        'fullName': 'Administrator AquaTemp',
      },
      {
        'email': '<EMAIL>',
        'password': 'operator123',
        'fullName': 'Operator Sistem',
      },
      {
        'email': '<EMAIL>',
        'password': 'teknisi123',
        'fullName': 'Teknisi Lapangan',
      },
    ];

    final results = <Map<String, dynamic>>[];

    for (final account in accounts) {
      final result = await createAdmin(
        email: account['email']!,
        password: account['password']!,
        fullName: account['fullName']!,
      );
      results.add({
        ...result,
        'email': account['email'],
        'password': account['password'],
      });
    }

    return results;
  }

  /// Widget untuk UI membuat akun admin (untuk development)
  static Widget buildCreateAccountUI() {
    return const CreateAccountWidget();
  }
}

class CreateAccountWidget extends StatefulWidget {
  const CreateAccountWidget({super.key});

  @override
  State<CreateAccountWidget> createState() => _CreateAccountWidgetState();
}

class _CreateAccountWidgetState extends State<CreateAccountWidget> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _createAccount() async {
    if (_emailController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Semua field harus diisi'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final result = await CreateAdminAccount.createAdmin(
      email: _emailController.text.trim(),
      password: _passwordController.text,
      fullName: _nameController.text.trim(),
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: result['success'] ? Colors.green : Colors.red,
        ),
      );

      if (result['success']) {
        _emailController.clear();
        _passwordController.clear();
        _nameController.clear();
      }
    }
  }

  Future<void> _createDemoAccounts() async {
    setState(() {
      _isLoading = true;
    });

    final results = await CreateAdminAccount.createDemoAccounts();

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Hasil Pembuatan Akun Demo'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: results.map((result) {
                return ListTile(
                  leading: Icon(
                    result['success'] ? Icons.check_circle : Icons.error,
                    color: result['success'] ? Colors.green : Colors.red,
                  ),
                  title: Text(result['email']),
                  subtitle: Text(result['message']),
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buat Akun Admin'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nama Lengkap',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createAccount,
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Buat Akun'),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createDemoAccounts,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('Buat Akun Demo'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
