class HeaterHistory {
  final String userId;
  final String statusId;
  final DateTime tanggal;
  final int totalWaktuMenyala; // dalam menit
  final String heaterId;
  final DateTime? createdAt;

  HeaterHistory({
    required this.userId,
    required this.statusId,
    required this.tanggal,
    required this.totalWaktuMenyala,
    required this.heaterId,
    this.createdAt,
  });

  factory HeaterHistory.fromJson(Map<String, dynamic> json) {
    return HeaterHistory(
      userId: json['user_id'] as String,
      statusId: json['status_id'] as String,
      tanggal: DateTime.parse(json['tanggal'] as String),
      totalWaktuMenyala: json['total_waktu_menyala'] as int,
      heaterId: json['heater_id'] as String,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'status_id': statusId,
      'tanggal': tanggal.toIso8601String(),
      'total_waktu_menyala': totalWaktuMenyala,
      'heater_id': heaterId,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Getter untuk format waktu yang mudah dibaca
  String get formattedDuration {
    if (totalWaktuMenyala < 60) {
      return '$totalWaktuMenyala menit';
    } else {
      final hours = totalWaktuMenyala ~/ 60;
      final minutes = totalWaktuMenyala % 60;
      if (minutes == 0) {
        return '$hours jam';
      } else {
        return '$hours jam $minutes menit';
      }
    }
  }

  // Getter untuk durasi dalam jam (decimal)
  double get durationInHours {
    return totalWaktuMenyala / 60.0;
  }

  // Method untuk menghitung efisiensi (contoh: berdasarkan target waktu)
  double calculateEfficiency(int targetMinutes) {
    if (targetMinutes == 0) return 0.0;
    return (totalWaktuMenyala / targetMinutes) * 100;
  }

  // Method untuk cek apakah penggunaan berlebihan
  bool isExcessiveUsage(int maxMinutesPerDay) {
    return totalWaktuMenyala > maxMinutesPerDay;
  }

  HeaterHistory copyWith({
    String? userId,
    String? statusId,
    DateTime? tanggal,
    int? totalWaktuMenyala,
    String? heaterId,
    DateTime? createdAt,
  }) {
    return HeaterHistory(
      userId: userId ?? this.userId,
      statusId: statusId ?? this.statusId,
      tanggal: tanggal ?? this.tanggal,
      totalWaktuMenyala: totalWaktuMenyala ?? this.totalWaktuMenyala,
      heaterId: heaterId ?? this.heaterId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'HeaterHistory(heaterId: $heaterId, tanggal: $tanggal, totalWaktuMenyala: $formattedDuration)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HeaterHistory && 
           other.heaterId == heaterId && 
           other.tanggal.day == tanggal.day &&
           other.tanggal.month == tanggal.month &&
           other.tanggal.year == tanggal.year;
  }

  @override
  int get hashCode => Object.hash(heaterId, tanggal.day, tanggal.month, tanggal.year);
}

// Class untuk statistik penggunaan heater
class HeaterUsageStats {
  final int totalDays;
  final int totalMinutes;
  final double averageMinutesPerDay;
  final int maxMinutesInDay;
  final int minMinutesInDay;
  final DateTime periodStart;
  final DateTime periodEnd;

  HeaterUsageStats({
    required this.totalDays,
    required this.totalMinutes,
    required this.averageMinutesPerDay,
    required this.maxMinutesInDay,
    required this.minMinutesInDay,
    required this.periodStart,
    required this.periodEnd,
  });

  String get formattedTotalDuration {
    if (totalMinutes < 60) {
      return '$totalMinutes menit';
    } else {
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      if (minutes == 0) {
        return '$hours jam';
      } else {
        return '$hours jam $minutes menit';
      }
    }
  }

  double get totalHours => totalMinutes / 60.0;
  double get averageHoursPerDay => averageMinutesPerDay / 60.0;

  static HeaterUsageStats fromHistoryList(List<HeaterHistory> histories) {
    if (histories.isEmpty) {
      return HeaterUsageStats(
        totalDays: 0,
        totalMinutes: 0,
        averageMinutesPerDay: 0,
        maxMinutesInDay: 0,
        minMinutesInDay: 0,
        periodStart: DateTime.now(),
        periodEnd: DateTime.now(),
      );
    }

    final sortedHistories = List<HeaterHistory>.from(histories)
      ..sort((a, b) => a.tanggal.compareTo(b.tanggal));

    final totalMinutes = histories.fold<int>(0, (sum, h) => sum + h.totalWaktuMenyala);
    final totalDays = histories.length;
    final averageMinutesPerDay = totalDays > 0 ? totalMinutes / totalDays : 0.0;
    
    final minutesList = histories.map((h) => h.totalWaktuMenyala).toList();
    final maxMinutes = minutesList.isNotEmpty ? minutesList.reduce((a, b) => a > b ? a : b) : 0;
    final minMinutes = minutesList.isNotEmpty ? minutesList.reduce((a, b) => a < b ? a : b) : 0;

    return HeaterUsageStats(
      totalDays: totalDays,
      totalMinutes: totalMinutes,
      averageMinutesPerDay: averageMinutesPerDay,
      maxMinutesInDay: maxMinutes,
      minMinutesInDay: minMinutes,
      periodStart: sortedHistories.first.tanggal,
      periodEnd: sortedHistories.last.tanggal,
    );
  }
}
