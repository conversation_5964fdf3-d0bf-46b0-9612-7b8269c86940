class Employee {
  final String userId;
  final String email;
  final String name;
  final String? profileImageUrl;
  final String id;
  final String role;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Employee({
    required this.userId,
    required this.email,
    required this.name,
    this.profileImageUrl,
    required this.id,
    required this.role,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      userId: json['user_id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      profileImageUrl: json['profile_image_url'] as String?,
      id: json['id'] as String,
      role: json['role'] as String,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email': email,
      'name': name,
      'profile_image_url': profileImageUrl,
      'id': id,
      'role': role,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Employee copyWith({
    String? userId,
    String? email,
    String? name,
    String? profileImageUrl,
    String? id,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      id: id ?? this.id,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Employee(id: $id, name: $name, email: $email, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
