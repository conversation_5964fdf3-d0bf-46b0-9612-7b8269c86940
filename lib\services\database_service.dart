import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../models/user.dart';
import '../models/employee.dart';
import '../models/sensor_data.dart';
import '../models/temperature_setting.dart';
import '../models/heater_status.dart';
import '../models/heater_history.dart';

class DatabaseService {
  static final _supabase = Supabase.instance.client;

  // Singleton pattern
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // Getter untuk current user
  User? get currentUser => _supabase.auth.currentUser;
  String? get currentUserId => _supabase.auth.currentUser?.id;

  // ==================== USER OPERATIONS ====================

  /// Mendapatkan data user berdasarkan ID
  Future<User?> getUserById(String userId) async {
    try {
      final response = await _supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();
      
      return User.fromJson(response);
    } catch (e) {
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  /// Membuat atau update user
  Future<User?> upsertUser(User user) async {
    try {
      final response = await _supabase
          .from('users')
          .upsert(user.toJson())
          .select()
          .single();
      
      return User.fromJson(response);
    } catch (e) {
      debugPrint('Error upserting user: $e');
      return null;
    }
  }

  /// Update profile user
  Future<bool> updateUserProfile({
    required String userId,
    String? namaLengkap,
    String? profileImageUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      if (namaLengkap != null) updateData['nama_lengkap'] = namaLengkap;
      if (profileImageUrl != null) updateData['profile_image_url'] = profileImageUrl;

      await _supabase
          .from('users')
          .update(updateData)
          .eq('id', userId);
      
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  // ==================== EMPLOYEE OPERATIONS ====================

  /// Mendapatkan data employee berdasarkan user ID
  Future<Employee?> getEmployeeByUserId(String userId) async {
    try {
      final response = await _supabase
          .from('employees')
          .select('*')
          .eq('user_id', userId)
          .single();
      
      return Employee.fromJson(response);
    } catch (e) {
      debugPrint('Error getting employee: $e');
      return null;
    }
  }

  /// Mendapatkan semua employees
  Future<List<Employee>> getAllEmployees() async {
    try {
      final response = await _supabase
          .from('employees')
          .select('*')
          .order('created_at', ascending: false);
      
      return response.map<Employee>((json) => Employee.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting employees: $e');
      return [];
    }
  }

  // ==================== SENSOR DATA OPERATIONS ====================

  /// Mendapatkan data sensor terbaru
  Future<SensorData?> getLatestSensorData({String? userId}) async {
    try {
      var query = _supabase
          .from('sensor_data')
          .select('*')
          .order('tanggal_waktu', ascending: false)
          .limit(1);

      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      final response = await query.single();
      return SensorData.fromJson(response);
    } catch (e) {
      debugPrint('Error getting latest sensor data: $e');
      return null;
    }
  }

  /// Mendapatkan data sensor dalam rentang waktu
  Future<List<SensorData>> getSensorDataByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('sensor_data')
          .select('*')
          .gte('tanggal_waktu', startDate.toIso8601String())
          .lte('tanggal_waktu', endDate.toIso8601String())
          .order('tanggal_waktu', ascending: false);

      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return response.map<SensorData>((json) => SensorData.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting sensor data by date range: $e');
      return [];
    }
  }

  /// Insert data sensor baru
  Future<SensorData?> insertSensorData(SensorData sensorData) async {
    try {
      final response = await _supabase
          .from('sensor_data')
          .insert(sensorData.toJson())
          .select()
          .single();
      
      return SensorData.fromJson(response);
    } catch (e) {
      debugPrint('Error inserting sensor data: $e');
      return null;
    }
  }

  /// Stream data sensor real-time
  Stream<List<SensorData>> getSensorDataStream({String? userId}) {
    try {
      var query = _supabase
          .from('sensor_data')
          .stream(primaryKey: ['data_id'])
          .order('tanggal_waktu', ascending: false)
          .limit(50);

      return query.map((data) => 
          data.map<SensorData>((json) => SensorData.fromJson(json)).toList());
    } catch (e) {
      debugPrint('Error creating sensor data stream: $e');
      return Stream.value([]);
    }
  }

  // ==================== TEMPERATURE SETTINGS OPERATIONS ====================

  /// Mendapatkan pengaturan suhu user
  Future<TemperatureSetting?> getTemperatureSetting(String userId) async {
    try {
      final response = await _supabase
          .from('temperature_settings')
          .select('*')
          .eq('user_id', userId)
          .single();
      
      return TemperatureSetting.fromJson(response);
    } catch (e) {
      debugPrint('Error getting temperature setting: $e');
      return null;
    }
  }

  /// Update atau insert pengaturan suhu
  Future<TemperatureSetting?> upsertTemperatureSetting(TemperatureSetting setting) async {
    try {
      final response = await _supabase
          .from('temperature_settings')
          .upsert(setting.toJson())
          .select()
          .single();
      
      return TemperatureSetting.fromJson(response);
    } catch (e) {
      debugPrint('Error upserting temperature setting: $e');
      return null;
    }
  }

  // ==================== HEATER STATUS OPERATIONS ====================

  /// Mendapatkan status heater terbaru
  Future<HeaterStatus?> getLatestHeaterStatus({String? sensorId}) async {
    try {
      var query = _supabase
          .from('heater_status')
          .select('*')
          .order('updated_at', ascending: false)
          .limit(1);

      if (sensorId != null) {
        query = query.eq('sensor_id', sensorId);
      }

      final response = await query.single();
      return HeaterStatus.fromJson(response);
    } catch (e) {
      debugPrint('Error getting heater status: $e');
      return null;
    }
  }

  /// Update status heater
  Future<HeaterStatus?> updateHeaterStatus({
    required String statusId,
    required String status,
    String? sensorId,
  }) async {
    try {
      final updateData = {
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      if (sensorId != null) {
        updateData['sensor_id'] = sensorId;
      }

      final response = await _supabase
          .from('heater_status')
          .update(updateData)
          .eq('status_id', statusId)
          .select()
          .single();
      
      return HeaterStatus.fromJson(response);
    } catch (e) {
      debugPrint('Error updating heater status: $e');
      return null;
    }
  }

  /// Stream status heater real-time
  Stream<HeaterStatus?> getHeaterStatusStream({String? sensorId}) {
    try {
      var query = _supabase
          .from('heater_status')
          .stream(primaryKey: ['status_id'])
          .order('updated_at', ascending: false)
          .limit(1);

      return query.map((data) {
        if (data.isEmpty) return null;
        return HeaterStatus.fromJson(data.first);
      });
    } catch (e) {
      debugPrint('Error creating heater status stream: $e');
      return Stream.value(null);
    }
  }

  // ==================== HEATER HISTORY OPERATIONS ====================

  /// Mendapatkan riwayat heater
  Future<List<HeaterHistory>> getHeaterHistory({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('heater_history')
          .select('*')
          .order('tanggal', ascending: false);

      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (startDate != null) {
        query = query.gte('tanggal', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('tanggal', endDate.toIso8601String());
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return response.map<HeaterHistory>((json) => HeaterHistory.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting heater history: $e');
      return [];
    }
  }

  /// Insert riwayat heater
  Future<HeaterHistory?> insertHeaterHistory(HeaterHistory history) async {
    try {
      final response = await _supabase
          .from('heater_history')
          .insert(history.toJson())
          .select()
          .single();
      
      return HeaterHistory.fromJson(response);
    } catch (e) {
      debugPrint('Error inserting heater history: $e');
      return null;
    }
  }

  // ==================== UTILITY METHODS ====================

  /// Test koneksi database
  Future<bool> testConnection() async {
    try {
      await _supabase.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      debugPrint('Database connection test failed: $e');
      return false;
    }
  }

  /// Cleanup old data (optional)
  Future<bool> cleanupOldData({int daysToKeep = 30}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      // Hapus data sensor lama
      await _supabase
          .from('sensor_data')
          .delete()
          .lt('tanggal_waktu', cutoffDate.toIso8601String());
      
      // Hapus riwayat heater lama
      await _supabase
          .from('heater_history')
          .delete()
          .lt('tanggal', cutoffDate.toIso8601String());
      
      return true;
    } catch (e) {
      debugPrint('Error cleaning up old data: $e');
      return false;
    }
  }
}
