# Integrasi ESP32 dengan AquaTemp

## Overview
Sistem integrasi ESP32 dengan aplikasi Flutter AquaTemp untuk monitoring suhu air dan kontrol otomatis heater/pompa air menggunakan sensor DS18B20 dan relay.

## Hardware Requirements

### 1. Komponen Utama:
- **ESP32 Development Board** (ESP32-WROOM-32)
- **Sensor Suhu DS18B20** (Waterproof)
- **2x Relay Module** (5V, untuk heater dan pompa)
- **Heater** (Pemanas air)
- **Pompa Air** (Water pump)
- **Resistor 4.7kΩ** (Pull-up untuk DS18B20)
- **Breadboard/PCB** untuk koneksi
- **<PERSON><PERSON> jumper**
- **Power Supply** (5V untuk relay, 3.3V untuk ESP32)

### 2. Pin Configuration:
```
ESP32 Pin Layout:
- GPIO 15: DS18B20 Data Pin
- GPIO 2:  Heater Relay Control
- GPIO 4:  Pump Relay Control
- GPIO 2:  Built-in LED (Status indicator)
- 3.3V:    Power untuk DS18B20
- GND:     Ground
```

### 3. Wiring Diagram:
```
DS18B20 Sensor:
- VCC (Red)    → ESP32 3.3V
- Data (Yellow) → ESP32 GPIO 15 + 4.7kΩ resistor to 3.3V
- GND (Black)  → ESP32 GND

Relay Module 1 (Heater):
- VCC → 5V Power Supply
- GND → Common Ground
- IN  → ESP32 GPIO 2
- COM → Heater Live Wire
- NO  → Power Source Live

Relay Module 2 (Pump):
- VCC → 5V Power Supply  
- GND → Common Ground
- IN  → ESP32 GPIO 4
- COM → Pump Live Wire
- NO  → Power Source Live
```

## Software Setup

### 1. Arduino IDE Setup:
```bash
# Install ESP32 Board Package
# File → Preferences → Additional Board Manager URLs:
https://dl.espressif.com/dl/package_esp32_index.json

# Tools → Board → ESP32 Arduino → ESP32 Dev Module
```

### 2. Required Libraries:
```cpp
// Install via Library Manager:
- OneWire by Jim Studt
- DallasTemperature by Miles Burton  
- ArduinoJson by Benoit Blanchon
- ESP32 WebServer (included)
```

### 3. WiFi Configuration:
```cpp
// Update in aquatemp_esp32.ino:
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";
```

## Database Schema

### 1. Tabel ESP32 Devices:
```sql
CREATE TABLE esp32_devices (
  device_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_name TEXT NOT NULL,
  user_id UUID REFERENCES users(id),
  mac_address TEXT UNIQUE,
  ip_address TEXT,
  firmware_version TEXT,
  is_online BOOLEAN DEFAULT false,
  last_seen TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Tabel Relay Controls:
```sql
CREATE TABLE relay_controls (
  control_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id UUID REFERENCES esp32_devices(device_id),
  user_id UUID REFERENCES users(id),
  relay_pin INTEGER NOT NULL,
  relay_name TEXT NOT NULL,
  device_type TEXT NOT NULL, -- 'heater' atau 'pump'
  is_active BOOLEAN DEFAULT false,
  last_activated TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Tabel Pump Status:
```sql
CREATE TABLE pump_status (
  status_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sensor_id TEXT,
  status TEXT NOT NULL, -- 'ON' atau 'OFF'
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## API Endpoints ESP32

### 1. Health Check:
```
GET /api/ping
Response: {"status": "ok", "timestamp": 12345, "device_id": "ABC123"}
```

### 2. Sensor Data:
```
GET /api/sensor
Response: {
  "device_id": "ABC123",
  "temperature": 25.5,
  "humidity": 60.0,
  "water_level": 75.0,
  "timestamp": 12345,
  "heater_status": false,
  "pump_status": true
}
```

### 3. Control Command:
```
POST /api/command
Body: {
  "command": "control_relay",
  "parameters": {
    "pin": 2,
    "state": "ON",
    "device_type": "heater"
  }
}
Response: {
  "success": true,
  "message": "Relay controlled successfully"
}
```

### 4. Device Status:
```
GET /api/status
Response: {
  "device_id": "ABC123",
  "device_name": "ESP32 AquaTemp",
  "firmware_version": "1.0.0",
  "ip_address": "*************",
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "wifi_rssi": -45,
  "uptime": 123456,
  "free_heap": 200000,
  "temperature": 25.5,
  "heater_status": false,
  "pump_status": true
}
```

## Automatic Control Logic

### 1. Temperature Control Flow:
```
Sensor Reading → Temperature Analysis → Device Control

If Temperature < Min Threshold:
  - Turn ON Heater
  - Turn OFF Pump
  
If Temperature > Max Threshold:
  - Turn OFF Heater  
  - Turn ON Pump
  
If Temperature in Normal Range:
  - Turn OFF Heater
  - Turn OFF Pump
```

### 2. Safety Features:
- **Minimum Control Interval**: 30 detik antara aksi kontrol
- **Device Timeout**: 10 detik timeout untuk komunikasi
- **Auto Retry**: 3x retry untuk command yang gagal
- **Heartbeat Monitoring**: Cek status device setiap 30 detik
- **Error Logging**: Log semua error ke database

## Flutter Integration

### 1. Services yang Digunakan:
- `ESP32Service`: Komunikasi dengan ESP32
- `AutoControlService`: Logika kontrol otomatis
- `DatabaseService`: Penyimpanan data

### 2. Real-time Features:
- **Live Temperature Monitoring**: Update suhu setiap 10 detik
- **Device Status Monitoring**: Cek online/offline status
- **Relay Status Sync**: Sinkronisasi status relay real-time
- **Auto Control Feedback**: Feedback aksi kontrol otomatis

### 3. Manual Override:
- **Auto Mode Toggle**: Enable/disable mode otomatis
- **Manual Relay Control**: Kontrol manual saat auto mode off
- **Emergency Stop**: Stop semua device dengan satu tombol

## Installation Steps

### 1. Hardware Assembly:
1. Pasang DS18B20 di dalam air dengan kabel waterproof
2. Hubungkan relay ke heater dan pompa air
3. Wiring sesuai diagram di atas
4. Test koneksi dengan multimeter

### 2. Software Upload:
1. Buka Arduino IDE
2. Load file `aquatemp_esp32.ino`
3. Update WiFi credentials
4. Upload ke ESP32
5. Monitor Serial untuk debug

### 3. Flutter App Setup:
1. Run `flutter pub get` untuk install dependencies
2. Update database schema di Supabase
3. Register ESP32 device melalui app
4. Test komunikasi dan kontrol

### 4. Testing Checklist:
- [ ] ESP32 connect ke WiFi
- [ ] Sensor DS18B20 baca suhu dengan benar
- [ ] Relay heater dapat dikontrol
- [ ] Relay pompa dapat dikontrol  
- [ ] Flutter app dapat komunikasi dengan ESP32
- [ ] Auto control berfungsi sesuai setting suhu
- [ ] Data tersimpan ke database
- [ ] Real-time monitoring berjalan

## Troubleshooting

### 1. ESP32 Tidak Connect WiFi:
- Cek SSID dan password
- Pastikan WiFi 2.4GHz (bukan 5GHz)
- Cek jarak dari router
- Reset ESP32 dan coba lagi

### 2. Sensor Tidak Terbaca:
- Cek wiring DS18B20
- Pastikan pull-up resistor 4.7kΩ terpasang
- Test sensor dengan kode sederhana
- Ganti sensor jika rusak

### 3. Relay Tidak Bekerja:
- Cek power supply 5V untuk relay
- Test relay dengan manual trigger
- Cek koneksi GPIO ke relay IN pin
- Pastikan ground common

### 4. Flutter App Tidak Connect:
- Cek IP address ESP32
- Pastikan ESP32 dan phone di network yang sama
- Test ping ke IP ESP32
- Cek firewall settings

## Monitoring & Maintenance

### 1. Regular Checks:
- **Daily**: Cek suhu dan status device
- **Weekly**: Cek log error dan performance
- **Monthly**: Backup data dan update firmware
- **Quarterly**: Maintenance hardware dan kalibrasi sensor

### 2. Performance Metrics:
- **Response Time**: < 5 detik untuk command
- **Uptime**: > 99% online time
- **Accuracy**: ±0.5°C untuk sensor suhu
- **Reliability**: < 1% failed commands

### 3. Alerts & Notifications:
- Device offline > 5 menit
- Suhu di luar range normal > 10 menit
- Relay gagal dikontrol > 3x berturut-turut
- Sensor error atau tidak terbaca

## Security Considerations

### 1. Network Security:
- Gunakan WPA2/WPA3 untuk WiFi
- Isolasi ESP32 di VLAN terpisah jika memungkinkan
- Regular update firmware ESP32

### 2. API Security:
- Implement rate limiting
- Validate semua input command
- Log semua API access

### 3. Physical Security:
- Pasang ESP32 di enclosure tahan air
- Amankan kabel dari gangguan fisik
- Backup power untuk sistem kritikal
