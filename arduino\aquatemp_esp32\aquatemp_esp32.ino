#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <OneWire.h>
#include <DallasTemperature.h>

// WiFi Configuration
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Pin Configuration
#define ONE_WIRE_BUS 15        // DS18B20 data pin
#define HEATER_RELAY_PIN 2     // Relay pin untuk heater
#define PUMP_RELAY_PIN 4       // Relay pin untuk pompa air
#define LED_PIN 2              // Built-in LED

// Sensor Setup
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);

// Web Server
WebServer server(80);

// Global Variables
float currentTemperature = 0.0;
bool heaterStatus = false;
bool pumpStatus = false;
unsigned long lastSensorRead = 0;
unsigned long lastHeartbeat = 0;
const unsigned long SENSOR_INTERVAL = 5000;  // Read sensor every 5 seconds
const unsigned long HEARTBEAT_INTERVAL = 30000; // Send heartbeat every 30 seconds

// Device Info
String deviceId = "";
String deviceName = "ESP32 AquaTemp";
String firmwareVersion = "1.0.0";

void setup() {
  Serial.begin(115200);
  
  // Initialize pins
  pinMode(HEATER_RELAY_PIN, OUTPUT);
  pinMode(PUMP_RELAY_PIN, OUTPUT);
  pinMode(LED_PIN, OUTPUT);
  
  // Initialize relays to OFF
  digitalWrite(HEATER_RELAY_PIN, LOW);
  digitalWrite(PUMP_RELAY_PIN, LOW);
  digitalWrite(LED_PIN, LOW);
  
  // Initialize sensor
  sensors.begin();
  
  // Generate device ID from MAC address
  deviceId = WiFi.macAddress();
  deviceId.replace(":", "");
  
  // Connect to WiFi
  connectToWiFi();
  
  // Setup web server routes
  setupWebServer();
  
  Serial.println("ESP32 AquaTemp System Ready!");
  Serial.println("Device ID: " + deviceId);
  Serial.println("IP Address: " + WiFi.localIP().toString());
}

void loop() {
  // Handle web server
  server.handleClient();
  
  // Read sensor data
  if (millis() - lastSensorRead >= SENSOR_INTERVAL) {
    readTemperature();
    lastSensorRead = millis();
  }
  
  // Send heartbeat (if needed for future implementation)
  if (millis() - lastHeartbeat >= HEARTBEAT_INTERVAL) {
    sendHeartbeat();
    lastHeartbeat = millis();
  }
  
  // Blink LED to show system is running
  blinkLED();
}

void connectToWiFi() {
  Serial.println("Connecting to WiFi...");
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(1000);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("WiFi connected!");
    Serial.println("IP address: " + WiFi.localIP().toString());
  } else {
    Serial.println();
    Serial.println("WiFi connection failed!");
  }
}

void setupWebServer() {
  // CORS headers
  server.enableCORS(true);
  
  // API Routes
  server.on("/api/ping", HTTP_GET, handlePing);
  server.on("/api/sensor", HTTP_GET, handleGetSensorData);
  server.on("/api/command", HTTP_POST, handleCommand);
  server.on("/api/status", HTTP_GET, handleGetStatus);
  server.on("/api/info", HTTP_GET, handleGetDeviceInfo);
  
  // Start server
  server.begin();
  Serial.println("Web server started");
}

void handlePing() {
  DynamicJsonDocument doc(200);
  doc["status"] = "ok";
  doc["timestamp"] = millis();
  doc["device_id"] = deviceId;
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

void handleGetSensorData() {
  DynamicJsonDocument doc(500);
  
  doc["device_id"] = deviceId;
  doc["temperature"] = currentTemperature;
  doc["humidity"] = 50.0; // Placeholder - add humidity sensor if needed
  doc["water_level"] = 75.0; // Placeholder - add water level sensor if needed
  doc["timestamp"] = millis();
  doc["heater_status"] = heaterStatus;
  doc["pump_status"] = pumpStatus;
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

void handleCommand() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(1024);
    deserializeJson(doc, server.arg("plain"));
    
    String command = doc["command"];
    DynamicJsonDocument response(500);
    
    if (command == "control_relay") {
      int pin = doc["parameters"]["pin"];
      String state = doc["parameters"]["state"];
      String deviceType = doc["parameters"]["device_type"];
      
      bool success = controlRelay(pin, state == "ON", deviceType);
      
      response["success"] = success;
      response["message"] = success ? "Relay controlled successfully" : "Failed to control relay";
      response["pin"] = pin;
      response["state"] = state;
      response["device_type"] = deviceType;
      
    } else if (command == "get_temperature") {
      readTemperature();
      response["success"] = true;
      response["temperature"] = currentTemperature;
      
    } else {
      response["success"] = false;
      response["message"] = "Unknown command";
    }
    
    response["device_id"] = deviceId;
    response["timestamp"] = millis();
    
    String responseStr;
    serializeJson(response, responseStr);
    
    server.send(200, "application/json", responseStr);
  } else {
    server.send(400, "application/json", "{\"error\":\"No data received\"}");
  }
}

void handleGetStatus() {
  DynamicJsonDocument doc(500);
  
  doc["device_id"] = deviceId;
  doc["device_name"] = deviceName;
  doc["firmware_version"] = firmwareVersion;
  doc["ip_address"] = WiFi.localIP().toString();
  doc["mac_address"] = WiFi.macAddress();
  doc["wifi_rssi"] = WiFi.RSSI();
  doc["uptime"] = millis();
  doc["free_heap"] = ESP.getFreeHeap();
  doc["temperature"] = currentTemperature;
  doc["heater_status"] = heaterStatus;
  doc["pump_status"] = pumpStatus;
  doc["timestamp"] = millis();
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

void handleGetDeviceInfo() {
  DynamicJsonDocument doc(300);
  
  doc["device_id"] = deviceId;
  doc["device_name"] = deviceName;
  doc["firmware_version"] = firmwareVersion;
  doc["mac_address"] = WiFi.macAddress();
  doc["ip_address"] = WiFi.localIP().toString();
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

bool controlRelay(int pin, bool activate, String deviceType) {
  Serial.println("Controlling " + deviceType + " relay on pin " + String(pin) + " to " + (activate ? "ON" : "OFF"));
  
  if (pin == HEATER_RELAY_PIN) {
    digitalWrite(HEATER_RELAY_PIN, activate ? HIGH : LOW);
    heaterStatus = activate;
    Serial.println("Heater " + String(activate ? "ON" : "OFF"));
    return true;
  } else if (pin == PUMP_RELAY_PIN) {
    digitalWrite(PUMP_RELAY_PIN, activate ? HIGH : LOW);
    pumpStatus = activate;
    Serial.println("Pump " + String(activate ? "ON" : "OFF"));
    return true;
  }
  
  return false;
}

void readTemperature() {
  sensors.requestTemperatures();
  float temp = sensors.getTempCByIndex(0);
  
  if (temp != DEVICE_DISCONNECTED_C) {
    currentTemperature = temp;
    Serial.println("Temperature: " + String(currentTemperature) + "°C");
  } else {
    Serial.println("Error reading temperature sensor");
  }
}

void sendHeartbeat() {
  // This function can be used to send periodic updates to the server
  // For now, it just prints to serial
  Serial.println("Heartbeat - Temp: " + String(currentTemperature) + "°C, Heater: " + 
                 String(heaterStatus ? "ON" : "OFF") + ", Pump: " + 
                 String(pumpStatus ? "ON" : "OFF"));
}

void blinkLED() {
  static unsigned long lastBlink = 0;
  static bool ledState = false;
  
  if (millis() - lastBlink >= 1000) {
    ledState = !ledState;
    digitalWrite(LED_PIN, ledState);
    lastBlink = millis();
  }
}
