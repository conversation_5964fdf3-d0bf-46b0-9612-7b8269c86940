import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart' show supabaseUrl;

class SupabaseConnectionTest {
  static final _supabase = Supabase.instance.client;

  /// Test koneksi ke Supabase dan tampilkan informasi detail
  static Future<Map<String, dynamic>> testConnection() async {
    final Map<String, dynamic> result = {
      'isConnected': false,
      'supabaseUrl': '',
      'projectRef': '',
      'userInfo': null,
      'authStatus': '',
      'databaseTables': [],
      'error': null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // 1. Cek URL dan Project Reference
      result['supabaseUrl'] = supabaseUrl;
      result['projectRef'] = _extractProjectRef(supabaseUrl);

      // 2. Cek status autentikasi
      final user = _supabase.auth.currentUser;
      if (user != null) {
        result['userInfo'] = {
          'id': user.id,
          'email': user.email,
          'createdAt': user.createdAt,
          'lastSignInAt': user.lastSignInAt,
          'role': user.role,
        };
        result['authStatus'] = 'Authenticated';
      } else {
        result['authStatus'] = 'Not Authenticated';
      }

      // 3. Test koneksi database dengan query sederhana
      try {
        // Test query ke tabel yang ada
        final tables = await _testDatabaseTables();
        result['databaseTables'] = tables;
        result['isConnected'] = true;
      } catch (e) {
        result['error'] = 'Database connection failed: $e';
        result['isConnected'] = false;
      }
    } catch (e) {
      result['error'] = 'Connection test failed: $e';
      result['isConnected'] = false;
    }

    return result;
  }

  /// Extract project reference dari URL Supabase
  static String _extractProjectRef(String url) {
    try {
      final uri = Uri.parse(url);
      final host = uri.host;
      if (host.contains('.supabase.co')) {
        return host.split('.').first;
      }
      return 'Unknown';
    } catch (e) {
      return 'Error parsing URL';
    }
  }

  /// Test koneksi ke berbagai tabel database
  static Future<List<Map<String, dynamic>>> _testDatabaseTables() async {
    final List<Map<String, dynamic>> tableTests = [];

    // Daftar tabel yang akan ditest berdasarkan analisis kode
    final tables = [
      'sensor_data',
      'temperature_settings',
      'heater_status',
      'users'
    ];

    for (String tableName in tables) {
      try {
        // Test dengan query sederhana untuk setiap tabel
        final response = await _supabase.from(tableName).select('*').limit(1);

        tableTests.add({
          'tableName': tableName,
          'status': 'Connected',
          'recordCount': response.length,
          'error': null,
        });
      } catch (e) {
        tableTests.add({
          'tableName': tableName,
          'status': 'Failed',
          'recordCount': 0,
          'error': e.toString(),
        });
      }
    }

    return tableTests;
  }

  /// Mendapatkan informasi detail tentang semua tabel
  static Future<Map<String, dynamic>> getTableStructures() async {
    final Map<String, dynamic> result = {
      'tables': [],
      'error': null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // Daftar tabel yang diketahui dari kode aplikasi
      final knownTables = {
        'sensor_data': {
          'description': 'Tabel untuk menyimpan data sensor suhu air',
          'expectedColumns': [
            'sensor_id',
            'suhu_air',
            'tanggal_waktu',
            'user_id'
          ],
          'primaryKey': 'sensor_id',
          'usage': 'Menyimpan data suhu yang dibaca dari sensor setiap detik'
        },
        'temperature_settings': {
          'description': 'Tabel untuk pengaturan suhu minimum dan maksimum',
          'expectedColumns': [
            'pengaturan_id',
            'min_temperature',
            'max_temperature',
            'user_id',
            'updated_at'
          ],
          'primaryKey': 'pengaturan_id',
          'usage': 'Menyimpan pengaturan batas suhu yang diinginkan user'
        },
        'heater_status': {
          'description': 'Tabel untuk status pemanas (ON/OFF)',
          'expectedColumns': ['status_id', 'status', 'sensor_id'],
          'primaryKey': 'status_id',
          'usage': 'Mengontrol dan menyimpan status pemanas air'
        },
        'users': {
          'description': 'Tabel untuk data pengguna aplikasi',
          'expectedColumns': ['user_id', 'nama_lengkap', 'profile_image_url'],
          'primaryKey': 'user_id',
          'usage': 'Menyimpan informasi profil pengguna'
        }
      };

      for (String tableName in knownTables.keys) {
        try {
          // Test akses tabel dan ambil sample data
          final sampleData =
              await _supabase.from(tableName).select('*').limit(3);

          // Hitung total records
          final countData = await _supabase
              .from(tableName)
              .select('*')
              .limit(1000); // Ambil maksimal 1000 untuk estimasi

          final tableInfo = knownTables[tableName]!;

          result['tables'].add({
            'name': tableName,
            'description': tableInfo['description'],
            'primaryKey': tableInfo['primaryKey'],
            'usage': tableInfo['usage'],
            'expectedColumns': tableInfo['expectedColumns'],
            'status': 'accessible',
            'recordCount': countData.length,
            'sampleData': sampleData,
            'actualColumns':
                sampleData.isNotEmpty ? sampleData.first.keys.toList() : [],
            'error': null,
          });
        } catch (e) {
          final tableInfo = knownTables[tableName]!;
          result['tables'].add({
            'name': tableName,
            'description': tableInfo['description'],
            'primaryKey': tableInfo['primaryKey'],
            'usage': tableInfo['usage'],
            'expectedColumns': tableInfo['expectedColumns'],
            'status': 'error',
            'recordCount': 0,
            'sampleData': [],
            'actualColumns': [],
            'error': e.toString(),
          });
        }
      }
    } catch (e) {
      result['error'] = 'Failed to analyze tables: $e';
    }

    return result;
  }

  /// Test insert data ke tabel sensor_data
  static Future<bool> testInsertData() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase.from('sensor_data').insert({
        'suhu_air': '25.5',
        'tanggal_waktu': DateTime.now().toIso8601String(),
        'user_id': user.id,
      });

      return true;
    } catch (e) {
      debugPrint('Test insert failed: $e');
      return false;
    }
  }

  /// Test realtime subscription
  static Future<bool> testRealtimeConnection() async {
    try {
      final subscription = _supabase
          .from('sensor_data')
          .stream(primaryKey: ['sensor_id']).limit(1);

      // Test apakah stream bisa dibuat
      await subscription.first.timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      debugPrint('Realtime test failed: $e');
      return false;
    }
  }

  /// Membaca data dari tabel tertentu
  static Future<Map<String, dynamic>> readTableData(String tableName,
      {int limit = 10}) async {
    final Map<String, dynamic> result = {
      'tableName': tableName,
      'data': [],
      'totalRecords': 0,
      'error': null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // Ambil data dengan limit
      final data = await _supabase
          .from(tableName)
          .select('*')
          .order('created_at', ascending: false)
          .limit(limit);

      // Hitung total records (estimasi)
      final countData = await _supabase.from(tableName).select('*').limit(1000);

      result['data'] = data;
      result['totalRecords'] = countData.length;
    } catch (e) {
      // Jika error dengan order by created_at, coba tanpa order
      try {
        final data = await _supabase.from(tableName).select('*').limit(limit);

        final countData =
            await _supabase.from(tableName).select('*').limit(1000);

        result['data'] = data;
        result['totalRecords'] = countData.length;
      } catch (e2) {
        result['error'] = e2.toString();
      }
    }

    return result;
  }

  /// Membaca semua data dari semua tabel
  static Future<Map<String, dynamic>> readAllTablesData() async {
    final Map<String, dynamic> result = {
      'tables': {},
      'summary': {},
      'error': null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    final tables = [
      'sensor_data',
      'temperature_settings',
      'heater_status',
      'users'
    ];
    int totalRecords = 0;

    try {
      for (String tableName in tables) {
        final tableData = await readTableData(tableName, limit: 20);
        result['tables'][tableName] = tableData;

        if (tableData['error'] == null) {
          totalRecords += (tableData['totalRecords'] as int);
        }
      }

      result['summary'] = {
        'totalTables': tables.length,
        'totalRecords': totalRecords,
        'accessibleTables': result['tables']
            .values
            .where((table) => table['error'] == null)
            .length,
      };
    } catch (e) {
      result['error'] = 'Failed to read tables: $e';
    }

    return result;
  }

  /// Format data tabel untuk ditampilkan
  static String formatTableData(Map<String, dynamic> tableData) {
    final buffer = StringBuffer();
    final tableName = tableData['tableName'];
    final data = tableData['data'] as List<dynamic>;
    final totalRecords = tableData['totalRecords'];

    buffer.writeln('📋 TABLE: $tableName');
    buffer.writeln('📊 Total Records: $totalRecords');
    buffer.writeln('📄 Showing: ${data.length} records');
    buffer.writeln('');

    if (tableData['error'] != null) {
      buffer.writeln('❌ Error: ${tableData['error']}');
      return buffer.toString();
    }

    if (data.isEmpty) {
      buffer.writeln('📭 No data found');
      return buffer.toString();
    }

    // Header
    final columns = (data.first as Map<String, dynamic>).keys.toList();
    buffer.writeln('Columns: ${columns.join(' | ')}');
    buffer.writeln('${'=' * 50}');

    // Data rows
    for (int i = 0; i < data.length; i++) {
      final row = data[i] as Map<String, dynamic>;
      buffer.writeln('Row ${i + 1}:');
      for (String column in columns) {
        final value = row[column];
        buffer.writeln('  $column: $value');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }

  /// Tampilkan hasil test dalam format yang mudah dibaca
  static String formatTestResult(Map<String, dynamic> result) {
    final buffer = StringBuffer();

    buffer.writeln('=== SUPABASE CONNECTION TEST ===');
    buffer.writeln('Timestamp: ${result['timestamp']}');
    buffer.writeln('');

    buffer.writeln('🌐 CONNECTION INFO:');
    buffer.writeln('  URL: ${result['supabaseUrl']}');
    buffer.writeln('  Project: ${result['projectRef']}');
    buffer.writeln(
        '  Status: ${result['isConnected'] ? '✅ Connected' : '❌ Disconnected'}');
    buffer.writeln('');

    buffer.writeln('👤 AUTHENTICATION:');
    buffer.writeln('  Status: ${result['authStatus']}');
    if (result['userInfo'] != null) {
      final user = result['userInfo'];
      buffer.writeln('  User ID: ${user['id']}');
      buffer.writeln('  Email: ${user['email']}');
      buffer.writeln('  Role: ${user['role']}');
    }
    buffer.writeln('');

    buffer.writeln('🗄️ DATABASE TABLES:');
    final tables = result['databaseTables'] as List<Map<String, dynamic>>;
    for (var table in tables) {
      final status = table['status'] == 'Connected' ? '✅' : '❌';
      buffer.writeln(
          '  $status ${table['tableName']}: ${table['recordCount']} records');
      if (table['error'] != null) {
        buffer.writeln('    Error: ${table['error']}');
      }
    }

    if (result['error'] != null) {
      buffer.writeln('');
      buffer.writeln('❌ ERROR: ${result['error']}');
    }

    return buffer.toString();
  }
}
