# Kode Diagram AquaTemp IoT - Bahasa Indonesia

## 📋 **Cara Download Gambar Diagram**

### **Metode 1: Menggunakan Mermaid Live Editor**
1. <PERSON><PERSON> **[Mermaid Live Editor](https://mermaid.live/)**
2. Copy paste kode diagram dari bawah ini
3. Klik tombol **"Download"** atau **"Export"**
4. Pilih format: PNG, SVG, atau PDF
5. Gambar akan terdownload otomatis

### **Metode 2: Screenshot Manual**
1. **Screenshot** area diagram yang ditampilkan
2. Crop sesuai kebutuhan
3. Save sebagai PNG/JPG

---

## 🖼️ **Diagram 1: Alur Sistem Lengkap AquaTemp**

```mermaid
graph TD
    %% Layer Hardware
    A[ESP32 + DS18B20] --> B[Pembacaan Suhu]
    A --> C[Kontrol Relay]
    C --> D[Pemanas Air]
    C --> E[Pompa Air]
    
    %% Layer Komunikasi
    B --> F[Server HTTP API]
    F --> G[Jaringan WiFi]
    G --> H[Aplikasi Flutter]
    
    %% Layer Aplikasi
    H --> I[Layanan ESP32]
    H --> J[Layanan Sensor]
    H --> K[Layanan Kontrol Otomatis]
    H --> L[Layanan Database]
    H --> M[Layanan Autentikasi]
    
    %% Layer Database
    L --> N[Database Supabase]
    N --> O[data_sensor]
    N --> P[pengaturan_suhu]
    N --> Q[riwayat_pemanas]
    N --> R[perangkat_esp32]
    
    %% Logika Kontrol
    J --> S{Analisis Suhu}
    S -->|Terlalu Rendah| T[Pemanas HIDUP]
    S -->|Terlalu Tinggi| U[Pompa HIDUP]
    S -->|Normal| V[Keduanya MATI]
    
    T --> K
    U --> K
    V --> K
    
    K --> I
    I --> W[Perintah HTTP]
    W --> G
    
    %% Antarmuka Pengguna
    H --> X[Halaman Beranda]
    H --> Y[Halaman Pengaturan]
    H --> Z[Halaman Riwayat]
    H --> AA[Halaman Profil]
    
    %% Update Real-time
    J --> BB[Stream Real-time]
    BB --> X
    BB --> Y
    BB --> Z
    
    %% Autentikasi
    M --> CC[Login/Logout]
    CC --> DD[Sesi Pengguna]
    DD --> L
    
    %% Styling
    classDef hardware fill:#ff9999
    classDef komunikasi fill:#99ccff
    classDef aplikasi fill:#99ff99
    classDef database fill:#ffcc99
    classDef ui fill:#cc99ff
    
    class A,B,C,D,E hardware
    class F,G,W komunikasi
    class H,I,J,K,L,M aplikasi
    class N,O,P,Q,R database
    class X,Y,Z,AA,BB,CC,DD ui
```

---

## 🖼️ **Diagram 2: Alur Memulai Aplikasi**

```mermaid
flowchart TD
    A[Buka Aplikasi] --> B[main.dart]
    B --> C[Inisialisasi Supabase]
    C --> D{Supabase Terhubung?}
    D -->|Tidak| E[Tampilkan Error]
    D -->|Ya| F[Jalankan MyApp]
    
    F --> G[AuthWrapper]
    G --> H[Cek Status Login]
    H --> I{Pengguna Sudah Login?}
    
    I -->|Tidak| J[Halaman Login]
    J --> K[Input Email/Password]
    K --> L[LayananAuth.masuk]
    L --> M{Login Berhasil?}
    M -->|Tidak| N[Tampilkan Pesan Error]
    N --> K
    M -->|Ya| O[Buat Data Pengguna]
    
    I -->|Ya| O
    O --> P[Pindah ke Halaman Utama]
    P --> Q[Inisialisasi Layanan]
    
    Q --> R[LayananDatabase.init]
    Q --> S[LayananSensor.init]
    Q --> T[LayananESP32.init]
    Q --> U[LayananKontrolOtomatis.init]
    Q --> V[LayananPemanas.init]
    
    R --> W[Muat Pengaturan Pengguna]
    S --> X[Mulai Monitoring Sensor]
    T --> Y[Hubungkan ke ESP32]
    U --> Z[Aktifkan Kontrol Otomatis]
    V --> AA[Muat Status Pemanas]
    
    W --> BB[Layanan Siap]
    X --> BB
    Y --> BB
    Z --> BB
    AA --> BB
    
    BB --> CC[Tampilkan Halaman Beranda]
    CC --> DD[Mulai Update Real-time]
    DD --> EE[Aplikasi Siap Digunakan]
    
    %% Penanganan Error
    E --> FF[Coba Koneksi Ulang]
    FF --> C
    
    %% Styling
    classDef mulaiNode fill:#90EE90
    classDef prosesNode fill:#87CEEB
    classDef keputusanNode fill:#FFB6C1
    classDef errorNode fill:#FFA07A
    classDef selesaiNode fill:#98FB98
    
    class A mulaiNode
    class B,C,F,G,H,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,BB,CC,DD prosesNode
    class D,I,M keputusanNode
    class E,N,FF errorNode
    class EE selesaiNode
```

---

## 🖼️ **Diagram 3: Alur Kontrol Suhu Otomatis**

```mermaid
flowchart TD
    A[Sensor DS18B20 ESP32] --> B[Baca Suhu Air]
    B --> C[Kirim ke Aplikasi Flutter]
    C --> D[LayananSensor Terima Data]
    D --> E[Simpan ke Database]
    E --> F[Update Stream UI]
    
    D --> G[LayananKontrolOtomatis]
    G --> H{Mode Otomatis Aktif?}
    H -->|Tidak| I[Hanya Kontrol Manual]
    H -->|Ya| J[Ambil Pengaturan Suhu]
    
    J --> K[Muat Batas Min/Max]
    K --> L{Analisis Suhu}
    
    L -->|Suhu < Min| M[Suhu Terlalu Rendah]
    L -->|Suhu > Max| N[Suhu Terlalu Tinggi]
    L -->|Min ≤ Suhu ≤ Max| O[Suhu Normal]
    
    M --> P[Keputusan: Pemanas HIDUP, Pompa MATI]
    N --> Q[Keputusan: Pemanas MATI, Pompa HIDUP]
    O --> R[Keputusan: Keduanya MATI]
    
    P --> S{Cek Keamanan}
    Q --> S
    R --> S
    
    S -->|Aksi Terakhir < 30 detik| T[Lewati Aksi - Terlalu Cepat]
    S -->|Aman untuk Dilanjutkan| U[Eksekusi Perintah Kontrol]
    
    U --> V[LayananESP32.kontrolRelay]
    V --> W[Kirim Perintah HTTP ke ESP32]
    W --> X[ESP32 Terima Perintah]
    
    X --> Y{Jenis Perintah}
    Y -->|Kontrol Pemanas| Z[Kontrol GPIO 2]
    Y -->|Kontrol Pompa| AA[Kontrol GPIO 4]
    
    Z --> BB[Relay 1 - Pemanas]
    AA --> CC[Relay 2 - Pompa Air]
    
    BB --> DD[Perangkat Pemanas]
    CC --> EE[Perangkat Pompa Air]
    
    DD --> FF[Panaskan Air]
    EE --> GG[Dinginkan Air]
    
    FF --> HH[Perubahan Suhu]
    GG --> HH
    HH --> A
    
    %% Logging dan Feedback
    U --> II[Catat Aksi ke Database]
    II --> JJ[Update Riwayat Pemanas]
    JJ --> KK[Update Status UI]
    
    %% Penanganan Error
    W --> LL{Perintah Berhasil?}
    LL -->|Tidak| MM[Coba Ulang Perintah]
    LL -->|Ya| NN[Update Status]
    MM --> W
    NN --> II
    
    T --> OO[Tunggu Siklus Berikutnya]
    OO --> G
    
    %% Styling
    classDef sensorNode fill:#FFE4B5
    classDef prosesNode fill:#87CEEB
    classDef keputusanNode fill:#FFB6C1
    classDef aksiNode fill:#98FB98
    classDef hardwareNode fill:#FFA07A
    classDef errorNode fill:#F0E68C
    
    class A,B sensorNode
    class C,D,E,F,G,J,K,U,V,W,X,II,JJ,KK,NN prosesNode
    class H,L,S,Y,LL keputusanNode
    class M,N,O,P,Q,R,Z,AA aksiNode
    class BB,CC,DD,EE,FF,GG,HH hardwareNode
    class I,T,MM,OO errorNode
```

---

## 🖼️ **Diagram 4: Arsitektur Layer Layanan**

```mermaid
graph TB
    %% Layer UI
    subgraph UI["🖥️ Layer Antarmuka Pengguna"]
        A[Halaman Login]
        B[Halaman Beranda]
        C[Halaman Pengaturan Suhu]
        D[Halaman Riwayat]
        E[Halaman Profil]
        F[Halaman Kontrol ESP32]
    end
    
    %% Layer Layanan
    subgraph Layanan["⚙️ Layer Layanan"]
        G[Layanan Autentikasi]
        H[Layanan Database]
        I[Layanan Sensor]
        J[Layanan Pemanas]
        K[Layanan ESP32]
        L[Layanan Kontrol Otomatis]
    end
    
    %% Layer Model
    subgraph Model["📋 Layer Model"]
        M[Pengguna]
        N[Data Sensor]
        O[Pengaturan Suhu]
        P[Status Pemanas]
        Q[Riwayat Pemanas]
        R[Perangkat ESP32]
        S[Kontrol Relay]
        T[Status Pompa]
    end
    
    %% Layanan Eksternal
    subgraph Eksternal["🌐 Layanan Eksternal"]
        U[Supabase Auth]
        V[Database Supabase]
        W[Hardware ESP32]
    end
    
    %% Koneksi UI ke Layanan
    A --> G
    B --> H
    B --> I
    B --> J
    C --> H
    C --> I
    D --> H
    E --> G
    E --> H
    F --> K
    F --> L
    
    %% Koneksi Layanan ke Model
    G --> M
    H --> M
    H --> N
    H --> O
    H --> P
    H --> Q
    I --> N
    I --> O
    J --> P
    J --> Q
    K --> R
    K --> S
    L --> N
    L --> O
    L --> T
    
    %% Koneksi Layanan ke Eksternal
    G --> U
    H --> V
    I --> V
    J --> V
    K --> W
    K --> V
    L --> V
    
    %% Ketergantungan Antar Layanan
    I --> H
    J --> H
    K --> H
    L --> I
    L --> K
    L --> H
    
    %% Styling
    classDef uiClass fill:#E6F3FF,stroke:#0066CC,stroke-width:2px
    classDef layananClass fill:#F0FFF0,stroke:#228B22,stroke-width:2px
    classDef modelClass fill:#FFF8DC,stroke:#DAA520,stroke-width:2px
    classDef eksternalClass fill:#FFE4E1,stroke:#DC143C,stroke-width:2px
    
    class A,B,C,D,E,F uiClass
    class G,H,I,J,K,L layananClass
    class M,N,O,P,Q,R,S,T modelClass
    class U,V,W eksternalClass
```

---

## 🛠️ **Tools untuk Generate Gambar**

### **Online Tools:**
1. **[Mermaid Live Editor](https://mermaid.live/)** - Tool resmi
2. **[Mermaid Chart](https://www.mermaidchart.com/)** - Fitur advanced
3. **[Draw.io](https://app.diagrams.net/)** - Support import Mermaid

### **VS Code Extensions:**
1. **Mermaid Preview** - Preview dalam VS Code
2. **Mermaid Markdown Syntax Highlighting**
3. **Markdown Preview Enhanced**

### **Command Line Tools:**
```bash
# Install Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# Generate gambar
mmdc -i diagram.mmd -o diagram.png
```

---

## 📱 **Cara Menggunakan:**

1. **Copy kode diagram** yang ingin Anda download
2. **Paste ke Mermaid Live Editor**
3. **Klik tombol Download/Export**
4. **Pilih format** (PNG untuk presentasi, SVG untuk editing)
5. **Save ke komputer**

**Semua diagram sudah dalam Bahasa Indonesia dan siap untuk didownload!** 📊🖼️🇮🇩
