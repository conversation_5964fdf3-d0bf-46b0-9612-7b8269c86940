class RelayControl {
  final String controlId;
  final String deviceId;
  final String userId;
  final int relayPin;
  final String relayName;
  final String deviceType; // 'heater' atau 'pump'
  final bool isActive;
  final DateTime? lastActivated;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RelayControl({
    required this.controlId,
    required this.deviceId,
    required this.userId,
    required this.relayPin,
    required this.relayName,
    required this.deviceType,
    required this.isActive,
    this.lastActivated,
    this.createdAt,
    this.updatedAt,
  });

  factory RelayControl.fromJson(Map<String, dynamic> json) {
    return RelayControl(
      controlId: json['control_id'] as String,
      deviceId: json['device_id'] as String,
      userId: json['user_id'] as String,
      relayPin: json['relay_pin'] as int,
      relayName: json['relay_name'] as String,
      deviceType: json['device_type'] as String,
      isActive: json['is_active'] as bool? ?? false,
      lastActivated: json['last_activated'] != null 
          ? DateTime.parse(json['last_activated'] as String)
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'control_id': controlId,
      'device_id': deviceId,
      'user_id': userId,
      'relay_pin': relayPin,
      'relay_name': relayName,
      'device_type': deviceType,
      'is_active': isActive,
      'last_activated': lastActivated?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Method untuk aktivasi relay
  RelayControl activate() {
    return copyWith(
      isActive: true,
      lastActivated: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Method untuk deaktivasi relay
  RelayControl deactivate() {
    return copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );
  }

  // Method untuk toggle status
  RelayControl toggle() {
    return isActive ? deactivate() : activate();
  }

  RelayControl copyWith({
    String? controlId,
    String? deviceId,
    String? userId,
    int? relayPin,
    String? relayName,
    String? deviceType,
    bool? isActive,
    DateTime? lastActivated,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RelayControl(
      controlId: controlId ?? this.controlId,
      deviceId: deviceId ?? this.deviceId,
      userId: userId ?? this.userId,
      relayPin: relayPin ?? this.relayPin,
      relayName: relayName ?? this.relayName,
      deviceType: deviceType ?? this.deviceType,
      isActive: isActive ?? this.isActive,
      lastActivated: lastActivated ?? this.lastActivated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'RelayControl(controlId: $controlId, relayName: $relayName, deviceType: $deviceType, isActive: $isActive, pin: $relayPin)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RelayControl && other.controlId == controlId;
  }

  @override
  int get hashCode => controlId.hashCode;
}

enum RelayType {
  heater,
  pump,
  unknown,
}

extension RelayTypeExtension on RelayType {
  String get displayName {
    switch (this) {
      case RelayType.heater:
        return 'Pemanas';
      case RelayType.pump:
        return 'Pompa Air';
      case RelayType.unknown:
        return 'Tidak Diketahui';
    }
  }

  String get deviceTypeValue {
    switch (this) {
      case RelayType.heater:
        return 'heater';
      case RelayType.pump:
        return 'pump';
      case RelayType.unknown:
        return 'unknown';
    }
  }

  static RelayType fromString(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'heater':
      case 'pemanas':
        return RelayType.heater;
      case 'pump':
      case 'pompa':
      case 'pompa_air':
        return RelayType.pump;
      default:
        return RelayType.unknown;
    }
  }
}
