import 'dart:async';
import 'package:flutter/material.dart';
import '../models/sensor_data.dart';
import '../models/temperature_setting.dart';
import '../models/heater_history.dart';
import 'database_service.dart';
import 'esp32_service.dart';
import 'sensor_service.dart';

class AutoControlService {
  static final DatabaseService _databaseService = DatabaseService();
  static final ESP32Service _esp32Service = ESP32Service();
  static final SensorService _sensorService = SensorService();

  // Singleton pattern
  static final AutoControlService _instance = AutoControlService._internal();
  factory AutoControlService() => _instance;
  AutoControlService._internal();

  // Stream controllers
  final StreamController<AutoControlStatus> _statusController =
      StreamController<AutoControlStatus>.broadcast();

  // Current state
  bool _isAutoModeEnabled = true;
  bool _isHeaterActive = false;
  bool _isPumpActive = false;
  TemperatureSetting? _currentSettings;
  SensorData? _lastSensorData;
  Timer? _controlTimer;
  DateTime? _lastControlAction;

  // Getters
  Stream<AutoControlStatus> get statusStream => _statusController.stream;
  bool get isAutoModeEnabled => _isAutoModeEnabled;
  bool get isHeaterActive => _isHeaterActive;
  bool get isPumpActive => _isPumpActive;

  /// Initialize auto control service
  Future<void> initialize() async {
    try {
      await _loadSettings();
      _startAutoControl();
      _listenToSensorData();
    } catch (e) {
      debugPrint('Error initializing auto control service: $e');
    }
  }

  /// Load temperature settings
  Future<void> _loadSettings() async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId != null) {
        _currentSettings = await _databaseService.getTemperatureSetting(userId);
      }
    } catch (e) {
      debugPrint('Error loading temperature settings: $e');
    }
  }

  /// Listen to sensor data changes
  void _listenToSensorData() {
    _sensorService.latestDataStream.listen((sensorData) {
      if (sensorData != null) {
        _lastSensorData = sensorData;
        if (_isAutoModeEnabled) {
          _processTemperatureControl(sensorData);
        }
      }
    });
  }

  /// Start automatic control timer
  void _startAutoControl() {
    _controlTimer?.cancel();
    _controlTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isAutoModeEnabled && _lastSensorData != null) {
        _processTemperatureControl(_lastSensorData!);
      }
    });
  }

  /// Process temperature control logic
  Future<void> _processTemperatureControl(SensorData sensorData) async {
    try {
      if (_currentSettings == null) {
        await _loadSettings();
        if (_currentSettings == null) return;
      }

      final currentTemp = double.tryParse(sensorData.suhuAir) ?? 0.0;
      final status = _currentSettings!.getTemperatureStatus(currentTemp);

      // Prevent rapid switching (minimum 30 seconds between actions)
      if (_lastControlAction != null &&
          DateTime.now().difference(_lastControlAction!).inSeconds < 30) {
        return;
      }

      bool shouldActivateHeater = false;
      bool shouldActivatePump = false;

      switch (status) {
        case TemperatureStatus.tooLow:
          // Suhu terlalu rendah - aktifkan heater, matikan pump
          shouldActivateHeater = true;
          shouldActivatePump = false;
          break;

        case TemperatureStatus.tooHigh:
          // Suhu terlalu tinggi - aktifkan pump, matikan heater
          shouldActivateHeater = false;
          shouldActivatePump = true;
          break;

        case TemperatureStatus.normal:
          // Suhu normal - matikan keduanya
          shouldActivateHeater = false;
          shouldActivatePump = false;
          break;
      }

      // Control heater
      if (_isHeaterActive != shouldActivateHeater) {
        final heaterSuccess = await _esp32Service.controlRelay(
          deviceType: 'heater',
          activate: shouldActivateHeater,
        );

        if (heaterSuccess) {
          _isHeaterActive = shouldActivateHeater;
          _lastControlAction = DateTime.now();

          // Log action
          await _logControlAction(
            'heater',
            shouldActivateHeater,
            currentTemp,
            status,
          );
        }
      }

      // Control pump
      if (_isPumpActive != shouldActivatePump) {
        final pumpSuccess = await _esp32Service.controlRelay(
          deviceType: 'pump',
          activate: shouldActivatePump,
        );

        if (pumpSuccess) {
          _isPumpActive = shouldActivatePump;
          _lastControlAction = DateTime.now();

          // Log action
          await _logControlAction(
            'pump',
            shouldActivatePump,
            currentTemp,
            status,
          );
        }
      }

      // Update status
      _statusController.add(AutoControlStatus(
        isAutoMode: _isAutoModeEnabled,
        isHeaterActive: _isHeaterActive,
        isPumpActive: _isPumpActive,
        currentTemperature: currentTemp,
        temperatureStatus: status,
        lastUpdate: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error processing temperature control: $e');
    }
  }

  /// Log control action to database
  Future<void> _logControlAction(
    String deviceType,
    bool activated,
    double temperature,
    TemperatureStatus status,
  ) async {
    try {
      final userId = _databaseService.currentUserId;
      if (userId == null) return;

      // Create HeaterHistory object
      final history = HeaterHistory(
        userId: userId,
        statusId: 'auto_${DateTime.now().millisecondsSinceEpoch}',
        tanggal: DateTime.now(),
        totalWaktuMenyala: 0, // Will be updated when deactivated
        heaterId: '${deviceType}_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
      );

      // Log to heater_history table
      await _databaseService.insertHeaterHistory(history);

      debugPrint(
          'Auto control: $deviceType ${activated ? 'ON' : 'OFF'} - Temp: ${temperature}°C (${status.displayName})');
    } catch (e) {
      debugPrint('Error logging control action: $e');
    }
  }

  /// Enable/disable auto mode
  Future<void> setAutoMode(bool enabled) async {
    try {
      _isAutoModeEnabled = enabled;

      if (!enabled) {
        // Turn off all devices when auto mode is disabled
        await _esp32Service.controlRelay(deviceType: 'heater', activate: false);
        await _esp32Service.controlRelay(deviceType: 'pump', activate: false);
        _isHeaterActive = false;
        _isPumpActive = false;
      }

      _statusController.add(AutoControlStatus(
        isAutoMode: _isAutoModeEnabled,
        isHeaterActive: _isHeaterActive,
        isPumpActive: _isPumpActive,
        currentTemperature:
            double.tryParse(_lastSensorData?.suhuAir ?? '0') ?? 0.0,
        temperatureStatus: _currentSettings?.getTemperatureStatus(
                double.tryParse(_lastSensorData?.suhuAir ?? '0') ?? 0.0) ??
            TemperatureStatus.normal,
        lastUpdate: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error setting auto mode: $e');
    }
  }

  /// Manual control for heater
  Future<bool> manualControlHeater(bool activate) async {
    if (_isAutoModeEnabled) {
      debugPrint('Cannot manual control heater while auto mode is enabled');
      return false;
    }

    try {
      final success = await _esp32Service.controlRelay(
        deviceType: 'heater',
        activate: activate,
      );

      if (success) {
        _isHeaterActive = activate;
        await _logControlAction(
          'heater',
          activate,
          double.tryParse(_lastSensorData?.suhuAir ?? '0') ?? 0.0,
          TemperatureStatus.normal,
        );
      }

      return success;
    } catch (e) {
      debugPrint('Error manual control heater: $e');
      return false;
    }
  }

  /// Manual control for pump
  Future<bool> manualControlPump(bool activate) async {
    if (_isAutoModeEnabled) {
      debugPrint('Cannot manual control pump while auto mode is enabled');
      return false;
    }

    try {
      final success = await _esp32Service.controlRelay(
        deviceType: 'pump',
        activate: activate,
      );

      if (success) {
        _isPumpActive = activate;
        await _logControlAction(
          'pump',
          activate,
          double.tryParse(_lastSensorData?.suhuAir ?? '0') ?? 0.0,
          TemperatureStatus.normal,
        );
      }

      return success;
    } catch (e) {
      debugPrint('Error manual control pump: $e');
      return false;
    }
  }

  /// Update temperature settings
  Future<void> updateTemperatureSettings(TemperatureSetting settings) async {
    try {
      _currentSettings = settings;

      // Immediately process control with new settings
      if (_isAutoModeEnabled && _lastSensorData != null) {
        await _processTemperatureControl(_lastSensorData!);
      }
    } catch (e) {
      debugPrint('Error updating temperature settings: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _controlTimer?.cancel();
    _statusController.close();
  }
}

class AutoControlStatus {
  final bool isAutoMode;
  final bool isHeaterActive;
  final bool isPumpActive;
  final double currentTemperature;
  final TemperatureStatus temperatureStatus;
  final DateTime lastUpdate;

  AutoControlStatus({
    required this.isAutoMode,
    required this.isHeaterActive,
    required this.isPumpActive,
    required this.currentTemperature,
    required this.temperatureStatus,
    required this.lastUpdate,
  });

  @override
  String toString() {
    return 'AutoControlStatus(auto: $isAutoMode, heater: $isHeaterActive, pump: $isPumpActive, temp: ${currentTemperature}°C, status: ${temperatureStatus.displayName})';
  }
}
