name: aquatemp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  animations: ^2.0.7

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  supabase_flutter: ^2.8.4
  image_picker: ^1.1.2
  fl_chart: ^0.71.0
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
  - family: Magilio
    fonts:
      - asset: assets/fonts/MagilioRegular-Yzv2O.ttf
        weight: 900
  - family: Roboto
    fonts:
      - asset: assets/fonts/Roboto-Black.ttf
        weight: 900
      - asset: assets/fonts/Roboto-BlackItalic.ttf
        weight: 900
        style: italic
      - asset: assets/fonts/Roboto-Bold.ttf
        weight: 700
      - asset: assets/fonts/Roboto-BoldItalic.ttf
        weight: 700
        style: italic
      - asset: assets/fonts/Roboto-ExtraBold.ttf
        weight: 800
      - asset: assets/fonts/Roboto-ExtraBoldItalic.ttf
        weight: 800
        style: italic
      - asset: assets/fonts/Roboto-ExtraLight.ttf
        weight: 200
      - asset: assets/fonts/Roboto-ExtraLightItalic.ttf
        weight: 200
        style: italic
      - asset: assets/fonts/Roboto-Italic.ttf
        style: italic
      - asset: assets/fonts/Roboto-Light.ttf
        weight: 300
      - asset: assets/fonts/Roboto-LightItalic.ttf
        weight: 300
        style: italic
      - asset: assets/fonts/Roboto-Medium.ttf
        weight: 500
      - asset: assets/fonts/Roboto-MediumItalic.ttf
        weight: 500
        style: italic
      - asset: assets/fonts/Roboto-Regular.ttf
      - asset: assets/fonts/Roboto-SemiBold.ttf
        weight: 600
      - asset: assets/fonts/Roboto-SemiBoldItalic.ttf
        weight: 600
        style: italic
      - asset: assets/fonts/Roboto-Thin.ttf
        weight: 100
      - asset: assets/fonts/Roboto-ThinItalic.ttf
        weight: 100
        style: italic

  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
