# AquaTemp IoT - Ringkasan Alur Aplikasi

## 🎯 Alur Utama Aplikasi

### 1. **Startup Flow** (Saat Aplikasi Dimulai)
```
App Launch → Supabase Init → Auth Check → Login/MainPage → Service Init → Ready
```

### 2. **Authentication Flow** (Proses Login)
```
LoginPage → Input Credentials → AuthService → Supabase Auth → User Data → MainPage
```

### 3. **Real-time Monitoring Flow** (Monitoring Berkelanjutan)
```
ESP32 Sensor → HTTP API → Flutter App → Database → UI Stream → User Display
```

### 4. **Automatic Control Flow** (Kontrol Otomatis)
```
Temperature Reading → Analysis → Decision → ESP32 Command → Relay Control → Device Action
```

---

## 🔄 Siklus Operasi Lengkap

### **Siklus 1: Data Collection (Setiap 10 detik)**
1. ESP32 baca suhu dari DS18B20
2. Kirim data via HTTP ke Flutter
3. SensorService terima dan proses data
4. Simpan ke database Supabase
5. Update UI real-time

### **Siklus 2: Auto Control (Setiap 5 detik)**
1. AutoControlService analisis suhu terbaru
2. Bandingkan dengan setting min/max
3. Tentukan aksi (heater ON/OFF, pump ON/OFF)
4. Kirim command ke ESP32
5. ESP32 kontrol relay
6. Log aktivitas ke database

### **Siklus 3: UI Update (Real-time)**
1. Database stream emit perubahan
2. Service layer terima update
3. UI rebuild otomatis
4. User lihat data terbaru

---

## 📱 Alur Navigasi User

### **HomePage (Dashboard Utama)**
- Tampil suhu real-time
- Status heater/pump
- Chart statistik
- Kontrol manual

### **UpdateSuhuPage (Pengaturan)**
- Set batas suhu min/max
- Validasi input
- Simpan ke database
- Update auto control

### **RiwayatPage (History)**
- Load data history
- Filter berdasarkan tanggal
- Tampil statistik usage

### **ProfilPage (User Profile)**
- Info user
- Edit profile
- Logout function
- Access ESP32 control

---

## 🔌 Alur Komunikasi Hardware

### **Flutter → ESP32 (Command)**
```
User Action → Service → HTTP POST → ESP32 Web Server → GPIO Control → Relay → Device
```

### **ESP32 → Flutter (Data)**
```
DS18B20 Sensor → ESP32 Read → HTTP Response → Flutter Service → Database → UI
```

### **Error Handling**
```
Command Failed → Retry Logic → Log Error → User Notification → Continue Operation
```

---

## 🗄️ Alur Database Operations

### **Write Operations**
```
User Input → Validation → Service → Supabase Insert/Update → Confirmation → UI Update
```

### **Read Operations**
```
UI Request → Service → Supabase Query → Data Transform → Model Object → UI Display
```

### **Real-time Streams**
```
Database Change → Supabase Stream → Service Listener → State Update → UI Rebuild
```

---

## ⚙️ Service Layer Interactions

### **Service Dependencies**
- **AuthService** ← UI Authentication
- **DatabaseService** ← All Services
- **SensorService** ← HomePage, AutoControl
- **ESP32Service** ← AutoControl, ESP32Page
- **AutoControlService** ← Background Process

### **Data Flow Between Services**
```
SensorService → AutoControlService → ESP32Service → Hardware
     ↓                    ↓                ↓
DatabaseService ← DatabaseService ← DatabaseService
     ↓                    ↓                ↓
UI Streams ←────── UI Streams ←────── UI Streams
```

---

## 🎛️ Control Logic Flow

### **Temperature Analysis**
```
Current Temp → Compare with Settings → Determine Status → Decide Action
```

### **Decision Matrix**
| Kondisi | Heater | Pump | Aksi |
|---------|--------|------|------|
| Suhu < Min | ON | OFF | Panaskan air |
| Suhu > Max | OFF | ON | Dinginkan air |
| Suhu Normal | OFF | OFF | Maintain |

### **Safety Mechanisms**
- Minimum 30 detik antar aksi
- Command timeout 10 detik
- Auto retry 3x untuk failed commands
- Error logging dan recovery

---

## 📊 State Management Flow

### **Local State**
```
User Action → Service Method → Update Local State → Emit Stream → UI Update
```

### **Persistent State**
```
Local State → Database Operation → Supabase Storage → Sync Across Devices
```

### **Real-time Sync**
```
Database Change → Real-time Stream → Service Update → UI Refresh
```

---

## 🔧 Error Handling Flow

### **Network Errors**
```
Request Failed → Check Connection → Retry Logic → Fallback → User Notification
```

### **Hardware Errors**
```
ESP32 Offline → Detection → Alert User → Switch to Manual → Wait for Recovery
```

### **Data Errors**
```
Invalid Data → Validation → Reject/Sanitize → Log Error → Continue Operation
```

---

## 🚀 Performance Optimization Flow

### **Efficient Data Loading**
- Lazy loading untuk history data
- Pagination untuk large datasets
- Caching untuk frequently accessed data

### **Real-time Optimization**
- Debouncing untuk rapid updates
- Stream subscription management
- Memory leak prevention

### **Network Optimization**
- Request batching
- Connection pooling
- Timeout management

---

## 📈 Monitoring & Analytics Flow

### **Performance Metrics**
```
Operation → Measure Time → Log Metrics → Analyze Trends → Optimize
```

### **Usage Analytics**
```
User Action → Event Logging → Data Aggregation → Insights → Improvements
```

### **Health Monitoring**
```
System Check → Status Report → Alert if Issues → Auto Recovery → Continue
```

---

## 🎯 Complete System Flow Summary

```
Physical World (Temperature) 
    ↓
Hardware Layer (ESP32 + DS18B20 + Relays)
    ↓
Communication Layer (WiFi + HTTP)
    ↓
Application Layer (Flutter + Services)
    ↓
Data Layer (Supabase + PostgreSQL)
    ↓
User Interface Layer (UI + Real-time Updates)
    ↓
User Experience (Monitoring + Control)
```

**AquaTemp IoT adalah sistem terintegrasi yang menggabungkan hardware, software, dan cloud services untuk menciptakan solusi monitoring dan kontrol suhu air yang otomatis dan real-time!** 🌊📱⚡
