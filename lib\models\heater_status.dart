class HeaterStatus {
  final String status;
  final String sensorId;
  final DateTime? updatedAt;
  final String statusId;

  HeaterStatus({
    required this.status,
    required this.sensorId,
    this.updatedAt,
    required this.statusId,
  });

  factory HeaterStatus.fromJson(Map<String, dynamic> json) {
    return HeaterStatus(
      status: json['status'] as String,
      sensorId: json['sensor_id'] as String,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      statusId: json['status_id'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'sensor_id': sensorId,
      'updated_at': updatedAt?.toIso8601String(),
      'status_id': statusId,
    };
  }

  // Getter untuk status boolean
  bool get isOn {
    return status.toLowerCase() == 'on' || status.toLowerCase() == 'aktif';
  }

  bool get isOff {
    return status.toLowerCase() == 'off' || status.toLowerCase() == 'nonaktif';
  }

  // Method untuk toggle status
  HeaterStatus toggleStatus() {
    final newStatus = isOn ? 'OFF' : 'ON';
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  // Method untuk set status spesifik
  HeaterStatus setStatus(bool turnOn) {
    final newStatus = turnOn ? 'ON' : 'OFF';
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  HeaterStatus copyWith({
    String? status,
    String? sensorId,
    DateTime? updatedAt,
    String? statusId,
  }) {
    return HeaterStatus(
      status: status ?? this.status,
      sensorId: sensorId ?? this.sensorId,
      updatedAt: updatedAt ?? this.updatedAt,
      statusId: statusId ?? this.statusId,
    );
  }

  @override
  String toString() {
    return 'HeaterStatus(statusId: $statusId, status: $status, sensorId: $sensorId, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HeaterStatus && other.statusId == statusId;
  }

  @override
  int get hashCode => statusId.hashCode;
}

enum HeaterState {
  on,
  off,
  unknown,
}

extension HeaterStateExtension on HeaterState {
  String get displayName {
    switch (this) {
      case HeaterState.on:
        return 'Aktif';
      case HeaterState.off:
        return 'Nonaktif';
      case HeaterState.unknown:
        return 'Tidak Diketahui';
    }
  }

  String get statusValue {
    switch (this) {
      case HeaterState.on:
        return 'ON';
      case HeaterState.off:
        return 'OFF';
      case HeaterState.unknown:
        return 'UNKNOWN';
    }
  }

  static HeaterState fromString(String status) {
    switch (status.toUpperCase()) {
      case 'ON':
      case 'AKTIF':
        return HeaterState.on;
      case 'OFF':
      case 'NONAKTIF':
        return HeaterState.off;
      default:
        return HeaterState.unknown;
    }
  }
}
