import 'dart:async';
import 'package:flutter/material.dart';
import '../models/sensor_data.dart';
import '../models/temperature_setting.dart';
import 'database_service.dart';

class SensorService {
  final DatabaseService _databaseService = DatabaseService();
  
  // Singleton pattern
  static final SensorService _instance = SensorService._internal();
  factory SensorService() => _instance;
  SensorService._internal();

  // Stream controllers untuk real-time data
  final StreamController<SensorData?> _latestDataController = 
      StreamController<SensorData?>.broadcast();
  final StreamController<TemperatureStatus> _temperatureStatusController = 
      StreamController<TemperatureStatus>.broadcast();

  // Getters untuk streams
  Stream<SensorData?> get latestDataStream => _latestDataController.stream;
  Stream<TemperatureStatus> get temperatureStatusStream => _temperatureStatusController.stream;

  // Cache untuk data terbaru
  SensorData? _latestSensorData;
  TemperatureSetting? _currentTemperatureSetting;

  // Timer untuk simulasi data (ji<PERSON> diperlukan)
  Timer? _simulationTimer;

  /// Initialize service dan mulai monitoring
  Future<void> initialize() async {
    try {
      // Load pengaturan suhu saat ini
      await _loadCurrentTemperatureSetting();
      
      // Load data sensor terbaru
      await _loadLatestSensorData();
      
      // Start real-time monitoring
      _startRealTimeMonitoring();
      
      debugPrint('SensorService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing SensorService: $e');
    }
  }

  /// Load pengaturan suhu saat ini
  Future<void> _loadCurrentTemperatureSetting() async {
    final userId = _databaseService.currentUserId;
    if (userId != null) {
      _currentTemperatureSetting = await _databaseService.getTemperatureSetting(userId);
    }
  }

  /// Load data sensor terbaru
  Future<void> _loadLatestSensorData() async {
    final userId = _databaseService.currentUserId;
    _latestSensorData = await _databaseService.getLatestSensorData(userId: userId);
    
    if (_latestSensorData != null) {
      _latestDataController.add(_latestSensorData);
      _checkTemperatureStatus(_latestSensorData!);
    }
  }

  /// Start real-time monitoring dari database
  void _startRealTimeMonitoring() {
    final userId = _databaseService.currentUserId;
    
    // Monitor sensor data stream
    _databaseService.getSensorDataStream(userId: userId).listen(
      (sensorDataList) {
        if (sensorDataList.isNotEmpty) {
          final latestData = sensorDataList.first;
          _latestSensorData = latestData;
          _latestDataController.add(latestData);
          _checkTemperatureStatus(latestData);
        }
      },
      onError: (error) {
        debugPrint('Error in sensor data stream: $error');
      },
    );
  }

  /// Cek status suhu dan kirim notifikasi jika perlu
  void _checkTemperatureStatus(SensorData sensorData) {
    if (_currentTemperatureSetting != null) {
      final status = _currentTemperatureSetting!.getTemperatureStatus(sensorData.suhuAirValue);
      _temperatureStatusController.add(status);
      
      // Log status untuk debugging
      debugPrint('Temperature Status: ${status.displayName} (${sensorData.suhuAirValue}°C)');
    }
  }

  /// Mendapatkan data sensor terbaru
  Future<SensorData?> getLatestSensorData() async {
    try {
      final userId = _databaseService.currentUserId;
      final data = await _databaseService.getLatestSensorData(userId: userId);
      
      if (data != null) {
        _latestSensorData = data;
        _latestDataController.add(data);
        _checkTemperatureStatus(data);
      }
      
      return data;
    } catch (e) {
      debugPrint('Error getting latest sensor data: $e');
      return null;
    }
  }

  /// Mendapatkan data sensor untuk hari ini
  Future<List<SensorData>> getTodaySensorData() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
      
      final userId = _databaseService.currentUserId;
      return await _databaseService.getSensorDataByDateRange(
        startDate: startOfDay,
        endDate: endOfDay,
        userId: userId,
      );
    } catch (e) {
      debugPrint('Error getting today sensor data: $e');
      return [];
    }
  }

  /// Mendapatkan data sensor untuk minggu ini
  Future<List<SensorData>> getWeeklySensorData() async {
    try {
      final now = DateTime.now();
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final startDate = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
      final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
      
      final userId = _databaseService.currentUserId;
      return await _databaseService.getSensorDataByDateRange(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
      );
    } catch (e) {
      debugPrint('Error getting weekly sensor data: $e');
      return [];
    }
  }

  /// Mendapatkan data sensor untuk bulan ini
  Future<List<SensorData>> getMonthlySensorData() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
      
      final userId = _databaseService.currentUserId;
      return await _databaseService.getSensorDataByDateRange(
        startDate: startOfMonth,
        endDate: endDate,
        userId: userId,
      );
    } catch (e) {
      debugPrint('Error getting monthly sensor data: $e');
      return [];
    }
  }

  /// Mendapatkan statistik suhu
  Future<TemperatureStatistics> getTemperatureStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final userId = _databaseService.currentUserId;
      
      // Default ke data 7 hari terakhir jika tidak ada range
      startDate ??= DateTime.now().subtract(const Duration(days: 7));
      endDate ??= DateTime.now();
      
      final sensorDataList = await _databaseService.getSensorDataByDateRange(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
      );
      
      return TemperatureStatistics.fromSensorDataList(sensorDataList);
    } catch (e) {
      debugPrint('Error getting temperature statistics: $e');
      return TemperatureStatistics.empty();
    }
  }

  /// Simulasi data sensor (untuk testing)
  void startDataSimulation({Duration interval = const Duration(seconds: 5)}) {
    _simulationTimer?.cancel();
    
    _simulationTimer = Timer.periodic(interval, (timer) async {
      final userId = _databaseService.currentUserId;
      if (userId == null) return;
      
      // Generate random sensor data
      final randomTemp = 20.0 + (DateTime.now().millisecondsSinceEpoch % 100) / 10.0;
      final randomHumidity = 40.0 + (DateTime.now().millisecondsSinceEpoch % 200) / 10.0;
      final randomWaterLevel = 50.0 + (DateTime.now().millisecondsSinceEpoch % 300) / 10.0;
      
      final sensorData = SensorData(
        suhuAir: randomTemp.toStringAsFixed(1),
        humidity: randomHumidity.toStringAsFixed(1),
        waterLevel: randomWaterLevel.toStringAsFixed(1),
        tanggalWaktu: DateTime.now(),
        userId: userId,
        dataId: 'sim_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
      );
      
      // Insert ke database
      await _databaseService.insertSensorData(sensorData);
      
      debugPrint('Simulated sensor data: ${sensorData.suhuAir}°C');
    });
  }

  /// Stop simulasi data
  void stopDataSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;
  }

  /// Update pengaturan suhu dan reload
  Future<void> updateTemperatureSetting(TemperatureSetting setting) async {
    try {
      final updatedSetting = await _databaseService.upsertTemperatureSetting(setting);
      if (updatedSetting != null) {
        _currentTemperatureSetting = updatedSetting;
        
        // Re-check status dengan pengaturan baru
        if (_latestSensorData != null) {
          _checkTemperatureStatus(_latestSensorData!);
        }
      }
    } catch (e) {
      debugPrint('Error updating temperature setting: $e');
    }
  }

  /// Mendapatkan pengaturan suhu saat ini
  TemperatureSetting? get currentTemperatureSetting => _currentTemperatureSetting;

  /// Mendapatkan data sensor terbaru dari cache
  SensorData? get cachedLatestSensorData => _latestSensorData;

  /// Dispose resources
  void dispose() {
    _latestDataController.close();
    _temperatureStatusController.close();
    _simulationTimer?.cancel();
  }
}

/// Class untuk statistik suhu
class TemperatureStatistics {
  final double averageTemperature;
  final double minTemperature;
  final double maxTemperature;
  final int totalReadings;
  final DateTime periodStart;
  final DateTime periodEnd;
  final List<double> temperatureValues;

  TemperatureStatistics({
    required this.averageTemperature,
    required this.minTemperature,
    required this.maxTemperature,
    required this.totalReadings,
    required this.periodStart,
    required this.periodEnd,
    required this.temperatureValues,
  });

  factory TemperatureStatistics.fromSensorDataList(List<SensorData> sensorDataList) {
    if (sensorDataList.isEmpty) {
      return TemperatureStatistics.empty();
    }

    final temperatures = sensorDataList.map((data) => data.suhuAirValue).toList();
    final sortedData = List<SensorData>.from(sensorDataList)
      ..sort((a, b) => a.tanggalWaktu.compareTo(b.tanggalWaktu));

    final sum = temperatures.fold<double>(0.0, (sum, temp) => sum + temp);
    final average = sum / temperatures.length;
    final min = temperatures.reduce((a, b) => a < b ? a : b);
    final max = temperatures.reduce((a, b) => a > b ? a : b);

    return TemperatureStatistics(
      averageTemperature: average,
      minTemperature: min,
      maxTemperature: max,
      totalReadings: temperatures.length,
      periodStart: sortedData.first.tanggalWaktu,
      periodEnd: sortedData.last.tanggalWaktu,
      temperatureValues: temperatures,
    );
  }

  factory TemperatureStatistics.empty() {
    final now = DateTime.now();
    return TemperatureStatistics(
      averageTemperature: 0.0,
      minTemperature: 0.0,
      maxTemperature: 0.0,
      totalReadings: 0,
      periodStart: now,
      periodEnd: now,
      temperatureValues: [],
    );
  }

  double get temperatureRange => maxTemperature - minTemperature;
  
  bool get hasData => totalReadings > 0;

  String get formattedAverage => averageTemperature.toStringAsFixed(1);
  String get formattedMin => minTemperature.toStringAsFixed(1);
  String get formattedMax => maxTemperature.toStringAsFixed(1);
  String get formattedRange => temperatureRange.toStringAsFixed(1);
}
