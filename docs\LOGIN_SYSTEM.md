# Sistem Login AquaTemp

## Overview
Sistem login AquaTemp menggunakan Supabase Authentication dengan akun yang dibuatkan oleh administrator. User tidak dapat mendaftar sendiri.

## Fitur Utama

### 1. Authentication Service (`lib/services/auth_service.dart`)
- **Login**: Email dan password authentication
- **Logout**: Sign out dengan konfirmasi
- **Auto-login**: Cek status login saat app dimulai
- **Error handling**: Pesan error yang user-friendly

### 2. Auth Wrapper (`lib/pages/auth_wrapper.dart`)
- Mengecek status authentication saat app dimulai
- Redirect otomatis ke login/main page
- Listen untuk perubahan auth state

### 3. Login Page (`lib/pages/login_page.dart`)
- UI login yang user-friendly
- Validasi input email dan password
- Loading state saat proses login
- Info untuk menghubungi admin jika belum punya akun

### 4. Logout dari Profil Page
- Tombol logout dengan konfirmasi dialog
- Redirect otomatis ke login page setelah logout

## Setup Database

### Tabel yang Diperlukan:
```sql
-- Tabel users (otomatis dibuat saat user login pertama kali)
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  nama_lengkap TEXT NOT NULL,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabel employees (opsional)
CREATE TABLE employees (
  employee_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  nama TEXT NOT NULL,
  posisi TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabel temperature_settings (dibuat otomatis dengan nilai default)
CREATE TABLE temperature_settings (
  setting_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  min_temperature DECIMAL(5,2) DEFAULT 25.0,
  max_temperature DECIMAL(5,2) DEFAULT 30.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Cara Membuat Akun User

### 1. Menggunakan Admin Panel (Development)
- Akses melalui tombol "Admin Panel (Development)" di halaman login
- Klik "Buat Akun Admin" untuk membuat akun individual
- Klik "Buat Akun Demo" untuk membuat 3 akun testing sekaligus

### 2. Akun Demo yang Tersedia:
```
Administrator:
- Email: <EMAIL>
- Password: admin123

Operator Sistem:
- Email: <EMAIL>  
- Password: operator123

Teknisi Lapangan:
- Email: <EMAIL>
- Password: teknisi123
```

### 3. Membuat Akun Manual di Supabase Dashboard:
1. Buka Supabase Dashboard → Authentication → Users
2. Klik "Add User" 
3. Masukkan email dan password
4. User akan otomatis dibuat di tabel `users` saat login pertama kali

## Flow Authentication

### 1. App Startup:
```
App Start → AuthWrapper → Check Auth Status
├── Logged In → MainPage
└── Not Logged In → LoginPage
```

### 2. Login Process:
```
LoginPage → Enter Credentials → AuthService.signIn()
├── Success → Navigate to MainPage
└── Error → Show Error Message
```

### 3. Logout Process:
```
ProfilPage → Logout Button → Confirmation Dialog
├── Confirm → AuthService.signOut() → Navigate to LoginPage
└── Cancel → Stay in ProfilPage
```

## Error Handling

### Pesan Error yang Ditangani:
- `Invalid login credentials` → "Email atau password salah"
- `Email not confirmed` → "Email belum diverifikasi. Hubungi admin"
- `User not found` → "Akun tidak ditemukan. Hubungi admin"
- `Too many requests` → "Terlalu banyak percobaan. Coba lagi nanti"

## Security Features

### 1. Input Validation:
- Email format validation
- Password tidak boleh kosong
- Trim whitespace dari input

### 2. Auth State Management:
- Real-time auth state listening
- Automatic redirect pada perubahan auth state
- Secure token handling oleh Supabase

### 3. User Data Protection:
- User data tersimpan terpisah per user ID
- Automatic data creation saat login pertama kali
- Secure logout dengan clear session

## Development vs Production

### Development Features (Harus Dihapus di Production):
- Admin Panel access dari login page
- Create account utilities
- Demo account credentials
- Development-only imports

### Production Checklist:
- [ ] Hapus `AdminPanel` dan `CreateAdminAccount`
- [ ] Hapus tombol "Admin Panel (Development)" dari login page
- [ ] Hapus akun demo dari dokumentasi
- [ ] Setup proper email verification di Supabase
- [ ] Configure proper error logging
- [ ] Setup proper backup untuk auth data

## Troubleshooting

### 1. Login Gagal:
- Cek koneksi internet
- Pastikan Supabase URL dan anon key benar
- Cek apakah akun sudah dibuat di Supabase Auth
- Cek console untuk error details

### 2. Auto-login Tidak Bekerja:
- Cek apakah AuthWrapper dipanggil di main.dart
- Pastikan auth state listener berjalan
- Cek apakah session masih valid

### 3. Data User Tidak Muncul:
- Cek apakah tabel `users` sudah dibuat
- Pastikan user ID match dengan auth.users
- Cek RLS (Row Level Security) policies di Supabase

## API Reference

### AuthService Methods:
```dart
// Login
Future<AuthResult> signInWithEmailPassword({
  required String email,
  required String password,
})

// Logout  
Future<bool> signOut()

// Check auth status
Future<bool> checkAuthStatus()

// Getters
User? get currentUser
String? get currentUserId  
bool get isLoggedIn
Stream<AuthState> get authStateChanges
```

### AuthResult Class:
```dart
class AuthResult {
  final bool success;
  final User? user;
  final String message;
}
```
