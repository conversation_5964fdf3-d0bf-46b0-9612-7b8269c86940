class PumpStatus {
  final String status;
  final String sensorId;
  final DateTime? updatedAt;
  final String statusId;

  PumpStatus({
    required this.status,
    required this.sensorId,
    this.updatedAt,
    required this.statusId,
  });

  factory PumpStatus.fromJson(Map<String, dynamic> json) {
    return PumpStatus(
      status: json['status'] as String,
      sensorId: json['sensor_id'] as String,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      statusId: json['status_id'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'sensor_id': sensorId,
      'updated_at': updatedAt?.toIso8601String(),
      'status_id': statusId,
    };
  }

  // Getter untuk status boolean
  bool get isOn {
    return status.toLowerCase() == 'on' || status.toLowerCase() == 'aktif';
  }

  bool get isOff {
    return status.toLowerCase() == 'off' || status.toLowerCase() == 'nonaktif';
  }

  // Method untuk toggle status
  PumpStatus toggleStatus() {
    final newStatus = isOn ? 'OFF' : 'ON';
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  // Method untuk set status spesifik
  PumpStatus setStatus(bool turnOn) {
    final newStatus = turnOn ? 'ON' : 'OFF';
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  PumpStatus copyWith({
    String? status,
    String? sensorId,
    DateTime? updatedAt,
    String? statusId,
  }) {
    return PumpStatus(
      status: status ?? this.status,
      sensorId: sensorId ?? this.sensorId,
      updatedAt: updatedAt ?? this.updatedAt,
      statusId: statusId ?? this.statusId,
    );
  }

  @override
  String toString() {
    return 'PumpStatus(statusId: $statusId, status: $status, sensorId: $sensorId, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PumpStatus && other.statusId == statusId;
  }

  @override
  int get hashCode => statusId.hashCode;
}

enum PumpState {
  on,
  off,
  unknown,
}

extension PumpStateExtension on PumpState {
  String get displayName {
    switch (this) {
      case PumpState.on:
        return 'Aktif';
      case PumpState.off:
        return 'Nonaktif';
      case PumpState.unknown:
        return 'Tidak Diketahui';
    }
  }

  String get statusValue {
    switch (this) {
      case PumpState.on:
        return 'ON';
      case PumpState.off:
        return 'OFF';
      case PumpState.unknown:
        return 'UNKNOWN';
    }
  }

  static PumpState fromString(String status) {
    switch (status.toUpperCase()) {
      case 'ON':
      case 'AKTIF':
        return PumpState.on;
      case 'OFF':
      case 'NONAKTIF':
        return PumpState.off;
      default:
        return PumpState.unknown;
    }
  }
}
