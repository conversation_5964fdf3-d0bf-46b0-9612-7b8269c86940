class ESP32Device {
  final String deviceId;
  final String deviceName;
  final String userId;
  final bool isOnline;
  final DateTime? lastSeen;
  final String? ipAddress;
  final String? macAddress;
  final String? firmwareVersion;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ESP32Device({
    required this.deviceId,
    required this.deviceName,
    required this.userId,
    required this.isOnline,
    this.lastSeen,
    this.ipAddress,
    this.macAddress,
    this.firmwareVersion,
    this.createdAt,
    this.updatedAt,
  });

  factory ESP32Device.fromJson(Map<String, dynamic> json) {
    return ESP32Device(
      deviceId: json['device_id'] as String,
      deviceName: json['device_name'] as String,
      userId: json['user_id'] as String,
      isOnline: json['is_online'] as bool? ?? false,
      lastSeen: json['last_seen'] != null 
          ? DateTime.parse(json['last_seen'] as String)
          : null,
      ipAddress: json['ip_address'] as String?,
      macAddress: json['mac_address'] as String?,
      firmwareVersion: json['firmware_version'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'device_name': deviceName,
      'user_id': userId,
      'is_online': isOnline,
      'last_seen': lastSeen?.toIso8601String(),
      'ip_address': ipAddress,
      'mac_address': macAddress,
      'firmware_version': firmwareVersion,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Method untuk update status online
  ESP32Device updateOnlineStatus(bool online) {
    return copyWith(
      isOnline: online,
      lastSeen: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Method untuk update IP address
  ESP32Device updateIpAddress(String ip) {
    return copyWith(
      ipAddress: ip,
      lastSeen: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  ESP32Device copyWith({
    String? deviceId,
    String? deviceName,
    String? userId,
    bool? isOnline,
    DateTime? lastSeen,
    String? ipAddress,
    String? macAddress,
    String? firmwareVersion,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ESP32Device(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      userId: userId ?? this.userId,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      ipAddress: ipAddress ?? this.ipAddress,
      macAddress: macAddress ?? this.macAddress,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ESP32Device(deviceId: $deviceId, deviceName: $deviceName, isOnline: $isOnline, ipAddress: $ipAddress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ESP32Device && other.deviceId == deviceId;
  }

  @override
  int get hashCode => deviceId.hashCode;
}

enum DeviceStatus {
  online,
  offline,
  unknown,
}

extension DeviceStatusExtension on DeviceStatus {
  String get displayName {
    switch (this) {
      case DeviceStatus.online:
        return 'Online';
      case DeviceStatus.offline:
        return 'Offline';
      case DeviceStatus.unknown:
        return 'Tidak Diketahui';
    }
  }

  static DeviceStatus fromBool(bool? isOnline) {
    if (isOnline == null) return DeviceStatus.unknown;
    return isOnline ? DeviceStatus.online : DeviceStatus.offline;
  }
}
