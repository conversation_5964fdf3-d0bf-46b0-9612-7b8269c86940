import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/supabase_test.dart';

class SupabaseTestPage extends StatefulWidget {
  const SupabaseTestPage({super.key});

  @override
  State<SupabaseTestPage> createState() => _SupabaseTestPageState();
}

class _SupabaseTestPageState extends State<SupabaseTestPage> {
  Map<String, dynamic>? _testResult;
  Map<String, dynamic>? _tableStructures;
  bool _isLoading = false;
  bool _isLoadingTables = false;
  String _testLog = '';

  @override
  void initState() {
    super.initState();
    _runConnectionTest();
    _loadTableStructures();
  }

  Future<void> _runConnectionTest() async {
    setState(() {
      _isLoading = true;
      _testLog = 'Memulai test koneksi Supabase...\n';
    });

    try {
      final result = await SupabaseConnectionTest.testConnection();
      final formattedResult = SupabaseConnectionTest.formatTestResult(result);

      setState(() {
        _testResult = result;
        _testLog = formattedResult;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testLog = 'Error during test: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testInsertData() async {
    setState(() {
      _testLog += '\n\n🧪 Testing data insert...\n';
    });

    final success = await SupabaseConnectionTest.testInsertData();
    setState(() {
      _testLog +=
          success ? '✅ Insert test successful\n' : '❌ Insert test failed\n';
    });
  }

  Future<void> _testRealtimeConnection() async {
    setState(() {
      _testLog += '\n\n📡 Testing realtime connection...\n';
    });

    final success = await SupabaseConnectionTest.testRealtimeConnection();
    setState(() {
      _testLog += success
          ? '✅ Realtime connection successful\n'
          : '❌ Realtime connection failed\n';
    });
  }

  Future<void> _readAllTablesData() async {
    setState(() {
      _testLog += '\n\n📖 Reading all tables data...\n';
    });

    try {
      final allData = await SupabaseConnectionTest.readAllTablesData();

      setState(() {
        _testLog += '\n=== DATABASE CONTENT ===\n';
        _testLog += 'Total Tables: ${allData['summary']['totalTables']}\n';
        _testLog += 'Total Records: ${allData['summary']['totalRecords']}\n';
        _testLog +=
            'Accessible Tables: ${allData['summary']['accessibleTables']}\n\n';

        // Tampilkan data setiap tabel
        final tables = allData['tables'] as Map<String, dynamic>;
        for (String tableName in tables.keys) {
          final tableData = tables[tableName];
          _testLog += SupabaseConnectionTest.formatTableData(tableData);
          _testLog += '\n${'=' * 60}\n\n';
        }
      });
    } catch (e) {
      setState(() {
        _testLog += '❌ Error reading tables: $e\n';
      });
    }
  }

  Future<void> _loadTableStructures() async {
    setState(() {
      _isLoadingTables = true;
    });

    try {
      final structures = await SupabaseConnectionTest.getTableStructures();
      setState(() {
        _tableStructures = structures;
        _isLoadingTables = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTables = false;
        _testLog += '\n❌ Error loading table structures: $e\n';
      });
    }
  }

  void _showTableStructures() {
    if (_tableStructures == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Table structures not loaded yet'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Database Tables'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: _buildTableStructuresView(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _testLog));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test result copied to clipboard'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Connection Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyToClipboard,
            tooltip: 'Copy to clipboard',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runConnectionTest,
            tooltip: 'Refresh test',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Cards
            if (_testResult != null) ...[
              Row(
                children: [
                  Expanded(
                    child: _buildStatusCard(
                      'Connection',
                      _testResult!['isConnected']
                          ? 'Connected'
                          : 'Disconnected',
                      _testResult!['isConnected'] ? Colors.green : Colors.red,
                      Icons.cloud,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatusCard(
                      'Auth Status',
                      _testResult!['authStatus'],
                      _testResult!['userInfo'] != null
                          ? Colors.green
                          : Colors.orange,
                      Icons.person,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Project Info Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Project Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('URL: ${_testResult!['supabaseUrl']}'),
                      Text('Project: ${_testResult!['projectRef']}'),
                      if (_testResult!['userInfo'] != null) ...[
                        const SizedBox(height: 8),
                        Text('User ID: ${_testResult!['userInfo']['id']}'),
                        Text('Email: ${_testResult!['userInfo']['email']}'),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testInsertData,
                    icon: const Icon(Icons.add),
                    label: const Text('Test Insert'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testRealtimeConnection,
                    icon: const Icon(Icons.wifi),
                    label: const Text('Test Realtime'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Table Structure Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoadingTables ? null : _showTableStructures,
                icon: _isLoadingTables
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.table_chart),
                label: Text(_isLoadingTables
                    ? 'Loading Tables...'
                    : 'View Database Tables'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Test Log
            const Text(
              'Test Log:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: _isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Running connection test...'),
                          ],
                        ),
                      )
                    : SingleChildScrollView(
                        child: Text(
                          _testLog,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableStructuresView() {
    if (_tableStructures == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final tables = _tableStructures!['tables'] as List<dynamic>;

    return ListView.builder(
      itemCount: tables.length,
      itemBuilder: (context, index) {
        final table = tables[index] as Map<String, dynamic>;
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            leading: Icon(
              table['status'] == 'accessible'
                  ? Icons.check_circle
                  : Icons.error,
              color:
                  table['status'] == 'accessible' ? Colors.green : Colors.red,
            ),
            title: Text(
              table['name'],
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(table['description']),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('📊 Records: ${table['recordCount']}'),
                    const SizedBox(height: 8),
                    Text('🔑 Primary Key: ${table['primaryKey']}'),
                    const SizedBox(height: 8),
                    Text('📝 Usage: ${table['usage']}'),
                    const SizedBox(height: 8),
                    const Text('📋 Columns:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    ...((table['actualColumns'] as List<dynamic>).isNotEmpty
                            ? (table['actualColumns'] as List<dynamic>)
                            : (table['expectedColumns'] as List<dynamic>))
                        .map((col) => Padding(
                              padding: const EdgeInsets.only(left: 16, top: 2),
                              child: Text('• $col'),
                            )),
                    if (table['error'] != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: ${table['error']}',
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusCard(
      String title, String status, Color color, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              status,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
