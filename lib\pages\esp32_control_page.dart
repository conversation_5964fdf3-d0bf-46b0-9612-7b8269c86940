import 'package:flutter/material.dart';
import '../services/esp32_service.dart';
import '../services/auto_control_service.dart';
import '../models/esp32_device.dart';
import '../models/relay_control.dart';

class ESP32ControlPage extends StatefulWidget {
  const ESP32ControlPage({super.key});

  @override
  State<ESP32ControlPage> createState() => _ESP32ControlPageState();
}

class _ESP32ControlPageState extends State<ESP32ControlPage> {
  final ESP32Service _esp32Service = ESP32Service();
  final AutoControlService _autoControlService = AutoControlService();
  
  ESP32Device? _currentDevice;
  List<RelayControl> _relayControls = [];
  bool _isAutoMode = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _listenToStreams();
  }

  Future<void> _initializeServices() async {
    await _esp32Service.initialize();
    await _autoControlService.initialize();
    
    setState(() {
      _currentDevice = _esp32Service.currentDevice;
      _relayControls = _esp32Service.relayControls;
      _isAutoMode = _autoControlService.isAutoModeEnabled;
      _isLoading = false;
    });
  }

  void _listenToStreams() {
    // Listen to device status
    _esp32Service.deviceStatusStream.listen((device) {
      if (mounted) {
        setState(() {
          _currentDevice = device;
        });
      }
    });

    // Listen to relay status
    _esp32Service.relayStatusStream.listen((relays) {
      if (mounted) {
        setState(() {
          _relayControls = relays;
        });
      }
    });

    // Listen to auto control status
    _autoControlService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _isAutoMode = status.isAutoMode;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kontrol ESP32'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDeviceStatusCard(),
                  const SizedBox(height: 16),
                  _buildAutoModeCard(),
                  const SizedBox(height: 16),
                  _buildRelayControlCard(),
                  const SizedBox(height: 16),
                  _buildDeviceRegistrationCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildDeviceStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.device_hub, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Status Device',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _currentDevice?.isOnline == true 
                        ? Colors.green 
                        : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _currentDevice?.isOnline == true ? 'Online' : 'Offline',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_currentDevice != null) ...[
              _buildInfoRow('Nama Device', _currentDevice!.deviceName),
              _buildInfoRow('IP Address', _currentDevice!.ipAddress ?? 'Tidak diketahui'),
              _buildInfoRow('MAC Address', _currentDevice!.macAddress ?? 'Tidak diketahui'),
              _buildInfoRow('Firmware', _currentDevice!.firmwareVersion ?? 'Tidak diketahui'),
              _buildInfoRow('Last Seen', _formatDateTime(_currentDevice!.lastSeen)),
            ] else ...[
              const Text(
                'Device belum terdaftar',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAutoModeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.auto_mode, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  'Mode Otomatis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _isAutoMode,
                  onChanged: (value) async {
                    await _autoControlService.setAutoMode(value);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isAutoMode 
                  ? 'Heater dan pompa akan dikontrol otomatis berdasarkan suhu'
                  : 'Kontrol manual - heater dan pompa dapat dikontrol secara manual',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRelayControlCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.electrical_services, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'Kontrol Relay',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_relayControls.isEmpty) ...[
              const Text(
                'Tidak ada relay yang terdaftar',
                style: TextStyle(color: Colors.grey),
              ),
            ] else ...[
              ..._relayControls.map((relay) => _buildRelayItem(relay)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRelayItem(RelayControl relay) {
    final isHeater = relay.deviceType == 'heater';
    final icon = isHeater ? Icons.whatshot : Icons.water_drop;
    final color = isHeater ? Colors.red : Colors.blue;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  relay.relayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Pin: ${relay.relayPin} | ${relay.deviceType.toUpperCase()}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: relay.isActive,
            onChanged: _isAutoMode 
                ? null // Disable manual control in auto mode
                : (value) async {
                    final success = relay.deviceType == 'heater'
                        ? await _autoControlService.manualControlHeater(value)
                        : await _autoControlService.manualControlPump(value);
                    
                    if (!success && mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Gagal mengontrol ${relay.relayName}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceRegistrationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.add_circle, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Registrasi Device',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Jika device ESP32 belum terdaftar, klik tombol di bawah untuk mendaftarkan device baru.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _currentDevice == null ? _showRegistrationDialog : null,
                icon: const Icon(Icons.add),
                label: const Text('Daftarkan Device Baru'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'Tidak diketahui',
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'Tidak diketahui';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showRegistrationDialog() {
    final nameController = TextEditingController();
    final macController = TextEditingController();
    final ipController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Daftarkan Device ESP32'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Nama Device',
                hintText: 'ESP32 AquaTemp',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: macController,
              decoration: const InputDecoration(
                labelText: 'MAC Address',
                hintText: 'AA:BB:CC:DD:EE:FF',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: ipController,
              decoration: const InputDecoration(
                labelText: 'IP Address',
                hintText: '*************',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isNotEmpty && macController.text.isNotEmpty) {
                final success = await _esp32Service.registerDevice(
                  deviceName: nameController.text,
                  macAddress: macController.text,
                  ipAddress: ipController.text.isNotEmpty ? ipController.text : null,
                );

                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(success 
                          ? 'Device berhasil didaftarkan' 
                          : 'Gagal mendaftarkan device'),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Daftar'),
          ),
        ],
      ),
    );
  }
}
